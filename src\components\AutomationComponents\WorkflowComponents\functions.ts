import { EditorState, Modifier } from "draft-js";
import { predefinedVariables } from "../../../utils/constants";
export interface CurlRequest {
  method: string;
  protocol: string;
  host: string;
  path: string;
  query: string | null;
  port: number | null;
  headers: { key: string; value: string }[];
  body: string;
  url: string;
}

const dictionary = {
  Name: "Name",
  CountryCode: "CountryCode",
  CountryName: "CountryName",
  Contact: "Contact",
  Email: "Email",
};

export const dictionaryOptions = Object.entries(dictionary).map(
  ([key, value]) => ({
    key,
    value,
  })
);

export const ButtonOptions = [
  { value: "", label: "None" },
  { value: "button", label: "Button" },
  { value: "list", label: "List" },
];

export const variableOptions = [
  // { value: "User Trait", label: "User Trait" },
  // { value: "Workflow Variable", label: "Workflow Variable" },
  { value: "variable", label: "Variable" },
];

export const SaveResponseRadioOptions = [
  // { value: "trait", label: "User Trait" },
  // { value: "variable", label: "Workflow Variable" },
  { value: "variable", label: "Variable" },
];

export interface Veriables {
  veriable: string;
  value: string;
  fallbackValue: string;
  type?: number;
  field?: string;
  index: number;
}

// export const getNextVariableCount = (editorState: EditorState): number => {
//   const content = editorState?.getCurrentContent()?.getPlainText();
//   const variableMatches = content?.match(/\{\{(\d+)\}\}/g);
//   if (!variableMatches) return 1;
//   const variableNumbers = variableMatches?.map((match) =>
//     parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
//   );
//   return Math.max(...variableNumbers) + 1;
// };

export const getNextVariableCount = (
  inputValue: EditorState | string,
  inputType: "editor" | "text"
): number => {
  let content = "";

  // Determine content based on inputType
  if (
    inputType === "editor" &&
    (inputValue as EditorState)?.getCurrentContent
  ) {
    // If input is of type editor, get the plain text from EditorState
    content = (inputValue as EditorState).getCurrentContent().getPlainText();
  } else if (inputType === "text") {
    // If input is plain string
    content = inputValue as string;
  }

  // Find matches for {{x}} variables
  const variableMatches = content.match(/\{\{(\d+)\}\}/g);
  if (!variableMatches) return 1;

  // Extract variable numbers
  const variableNumbers = variableMatches.map((match) =>
    parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
  );

  // Return the next variable count
  return Math.max(...variableNumbers) + 1;
};

export const validateVariables = (text: String) => {
  const variableMatches = text?.match(/\{\{(\d+)\}\}/g);
  const variableNumbers = variableMatches?.map((match) =>
    parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
  );
  let checkValidation = true;
  if (variableNumbers?.length) {
    if (variableNumbers[0] !== 1) {
      checkValidation = false;
      return checkValidation;
    }
    for (let i = 0; i < variableNumbers?.length - 1; i++) {
      if (Number(variableNumbers[i + 1]) !== Number(variableNumbers[i]) + 1) {
        checkValidation = false;
        return checkValidation;
      }
    }
  }
  return checkValidation;
};

// Get the count based on all header values, not just the specific index
export const getNextVariableCountFromAllHeaders = (headers: any[]) => {
  // Regular expression to find variables in the format {{n}}
  const variableRegex = /\{\{(\d+)\}\}/g;

  // Collect all variable numbers from the headers
  let maxVariableNumber = 0;

  headers.forEach((header) => {
    const matches = header?.value?.match(variableRegex);
    if (matches) {
      matches?.forEach((match: any) => {
        const number = parseInt(match?.replace(/[{}]/g, ""), 10);
        if (number > maxVariableNumber) {
          maxVariableNumber = number;
        }
      });
    }
  });

  // Return the next available variable count
  return maxVariableNumber + 1;
};

export const insertTextAtCursor = (
  editorState: EditorState,
  text: string
): EditorState => {
  const contentState = editorState.getCurrentContent();
  const selectionState = editorState.getSelection();
  const newContentState = Modifier.insertText(
    contentState,
    selectionState,
    text
  );
  return EditorState.push(editorState, newContentState, "insert-characters");
};

export const extractVariablesInWorkflows = (
  editorState: EditorState,
  existingVariables: any = []
): any => {
  const content = editorState.getCurrentContent().getPlainText();
  const variableRegex = /\{\{(\d+)\}\}/g;
  const matches: any = content.match(variableRegex);

  if (!matches)
    return existingVariables.filter((v: any) => content.includes(v.veriable));

  return matches?.map((match: any) => {
    const variable = match.replace(/\{\{|\}\}/g, "");
    const existingVar = existingVariables.find(
      (v: any) => v.veriable === `{{${variable}}}`
    );
    return (
      existingVar || {
        veriable: `{{${variable}}}`,
        type: 1,
        value: "",
        field: "",
        fallbackValue: "",
      }
    );
  });
};

// Validate exclusive variable usage
export const validateVariableExclusivity = (
  content: string,
  selectedType: "addVariable" | "leadratVariable" | null,
  showToast: (message: string) => void
): boolean => {
  const hasNormal = /\{\{\d+\}\}/.test(content);
  const hasPredefined = predefinedVariables.some((v) => content.includes(v));

  // Validate based on selected type
  if (selectedType === "addVariable" && hasPredefined) {
    if (showToast) {
      showToast(
        "Error: Predefined variables cannot be used when custom variables are selected"
      );
    }
    return false;
  }

  if (selectedType === "leadratVariable" && hasNormal) {
    if (showToast) {
      showToast(
        "Error: Custom variables cannot be used when predefined variables are selected"
      );
    }
    return false;
  }

  return true;
};

// Extract normal variables only
const extractNormalVariables = (
  content: string,
  existingNormalVars: any[]
): any[] => {
  const customRegex = /\{\{\d+\}\}/g;
  const matches: string[] = [];
  let match;

  while ((match = customRegex.exec(content)) !== null) {
    matches.push(match[0]);
  }

  if (!matches.length) {
    return existingNormalVars.filter((v) => content.includes(v.veriable));
  }

  return matches.map((match, index) => {
    const existingVar = existingNormalVars.find(
      (v: any) => v.veriable === match
    );

    return existingVar
      ? {
          ...existingVar,
          veriable: `{{${index + 1}}}`,
          index,
        }
      : {
          veriable: `{{${index + 1}}}`,
          type: 1,
          value: "",
          field: "",
          fallbackValue: "",
          referenceTableType: 0,
          index,
        };
  });
};

// Utility to validate a single predefined variable
const isValidPredefinedVariable = (text: string) => {
  // Must have exactly two '#' characters
  if ((text.match(/#/g) || []).length !== 2) return false;

  // Must start and end with '#'
  if (!text.startsWith("#") || !text.endsWith("#")) return false;

  // Must match exactly one of the predefined variables
  return predefinedVariables.includes(text);
};

// Extract predefined variables only
// Enhanced extraction of predefined variables
const extractPredefinedVariables = (
  content: string,
  existingPredefinedVars: any[]
): { cleanedContent: string; variables: any[] } => {
  // Find all occurrences of potential predefined variables
  const regex = /#[^#]+#/g;
  const matches: string[] = [];
  let match;

  // Find all matches including duplicates
  while ((match = regex.exec(content)) !== null) {
    if (predefinedVariables.includes(match[0])) {
      matches.push(match[0]);
    }
  }

  // Create a map of existing variables for quick lookup
  const existingVarMap = new Map<string, any>();
  existingPredefinedVars.forEach((v) => existingVarMap.set(v.veriable, v));

  // Process all found matches, including duplicates
  const result: any[] = [];

  matches.forEach((match, index) => {
    const existingVar = existingVarMap.get(match);
    result.push(
      existingVar || {
        veriable: match,
        type: 1,
        field: "",
        value: "",
        fallbackValue: "",
        referenceTableType: 0,
        index: index,
      }
    );
  });

  return {
    cleanedContent: content,
    variables: result,
  };
};

export const extractVariables = (
  editorState: EditorState,
  existingNormalVars: any[],
  existingPredefinedVars: any[],
  selectedType: "addVariable" | "leadratVariable" | null,
  showToast?: (message: string) => void
): {
  normalVariables: any[];
  predefinedVariables: any[];
  cleanedContent: string;
} => {
  let content = editorState.getCurrentContent().getPlainText();

  // Validate exclusive variable usage
  if (
    selectedType &&
    showToast &&
    !validateVariableExclusivity(content, selectedType, showToast)
  ) {
    return {
      normalVariables: existingNormalVars.filter((v) =>
        content.includes(v.veriable)
      ),
      predefinedVariables: existingPredefinedVars.filter((v) =>
        content.includes(v.veriable)
      ),
      cleanedContent: content,
    };
  }

  // Auto-detect type if not explicitly selected
  const effectiveType =
    selectedType ||
    (hasCustomVariables(content)
      ? "addVariable"
      : hasPredefinedVariables(content)
      ? "leadratVariable"
      : null);

  if (effectiveType === "addVariable") {
    return {
      normalVariables: extractNormalVariables(content, existingNormalVars),
      predefinedVariables: [], // Clear predefined variables
      cleanedContent: content,
    };
  }

  if (effectiveType === "leadratVariable") {
    const { cleanedContent, variables } = extractPredefinedVariables(
      content,
      existingPredefinedVars
    );
    console.log("cleanedContent", cleanedContent);
    console.log("variables", variables);
    return {
      normalVariables: [], // Clear normal variables
      predefinedVariables: variables,
      cleanedContent,
    };
  }

  // No variables detected - keep existing but filter by content
  return {
    normalVariables: existingNormalVars.filter((v) =>
      content.includes(v.veriable)
    ),
    predefinedVariables: existingPredefinedVars.filter((v) =>
      content.includes(v.veriable)
    ),
    cleanedContent: content,
  };
};

// Check if content contains any custom variables
export const hasCustomVariables = (content: string): boolean => {
  return /\{\{\d+\}\}/.test(content);
};

// Check if content contains any valid predefined variables
// Check if content contains any valid predefined variables
export const hasPredefinedVariables = (content: string): boolean => {
  const regex = /#[^#\s]+#/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    if (isValidPredefinedVariable(match[0])) {
      return true;
    }
  }
  return false;
};

// Create a helper function to safely replace variables
export const replaceVariablesSequentially = (content: string) => {
  const regex = /{{[0-9]+}}/g;
  const replacements = [];
  let match;
  let index = 1;

  // First, find all variables and their positions
  while ((match = regex.exec(content)) !== null) {
    replacements.push({
      original: match[0],
      new: `{{${index++}}}`,
      start: match.index,
      end: match.index + match[0].length,
    });
  }

  // Replace from last to first to preserve positions
  let updatedContent = content;
  for (let i = replacements.length - 1; i >= 0; i--) {
    const { original, new: newVar, start, end } = replacements[i];
    updatedContent =
      updatedContent.substring(0, start) +
      newVar +
      updatedContent.substring(end);
  }

  return updatedContent;
};

export const extractVeriablesFromString = (
  content: string,
  existingVeriables: Veriables[] = []
): Veriables[] => {
  const variableRegex = /\{\{(\d+)\}\}/g; // Regex to match {{number}}
  const matches = content?.match(variableRegex);

  if (!matches) {
    // If no matches found, filter the existing veriables
    return existingVeriables?.filter((v) => content?.includes(v.veriable));
  }

  // Map over the matches to extract veriable information
  return matches.map((match, index) => {
    const veriable = match.replace(/\{\{|\}\}/g, ""); // Extract number
    const existingVeriable = existingVeriables.find(
      (v) => v.veriable === `{{${veriable}}}`
    );
    return (
      existingVeriable || {
        veriable: `{{${veriable}}}`,
        type: 1,
        field: "",
        value: "",
        fallbackValue: "",
        index: index,
      }
    );
  });
};

export const validateLength = (inputString: string, length: number) => {
  if (inputString.length > length) {
    return `Length exceeds ${length} characters`;
  }
  return null; // No error if length is valid
};

export function parseCurlCommand(curlCommand: string): CurlRequest | null {
  let requestMethod = "GET";
  let protocol = "";
  let host = "";
  let path = "";
  let query: string | null = null;
  let port: number | null = null;
  const headers: { key: string; value: string }[] = [];
  let body = "";

  // Method
  const methodMatch = curlCommand.match(/(?:--request|-X)\s+([A-Z]+)/);
  if (methodMatch) requestMethod = methodMatch[1];

  // URL
  const urlMatch = curlCommand.match(/['"]?(https?:\/\/[^\s'"]+)['"]?/);
  if (!urlMatch) return null;

  let urlStr = urlMatch[1];

  try {
    const url = new URL(urlStr);
    protocol = url.protocol.replace(":", "");
    host = url.hostname;
    path = url.pathname || "/";
    query = url.search ? url.search.slice(1) : null;
    port = url.port ? parseInt(url.port) : null;
  } catch (err) {
    console.error("Invalid URL:", err);
    return null;
  }

  // Headers
  const headerRegex = /(?:--header|-H)\s+['"]?([^'"]+)['"]?/g;
  let headerMatch;
  while ((headerMatch = headerRegex.exec(curlCommand)) !== null) {
    const [name, value] = headerMatch[1].split(":").map((part) => part.trim());
    if (name && value) {
      if (
        name.toLowerCase() === "content-type" ||
        name.toLowerCase() === "accept"
      ) {
        headers.unshift({ key: name, value }); // Ensure Content-Type is at index 0
      } else {
        headers.push({ key: name, value });
      }
    }
  }

  // Body
  const bodyMatch = curlCommand.match(
    /(?:--data(?:-raw)?|-d)\s+(["'])([\s\S]*?)\1/
  );

  if (bodyMatch) {
    body = bodyMatch[2];
    if (requestMethod === "GET") requestMethod = "POST";
  }

  return {
    method: requestMethod,
    protocol,
    host,
    path,
    query,
    port,
    headers,
    body,
    url: urlStr,
  };
}
