import { Box, Button, IconButton, Paper, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { bgColors } from "../../../utils/bgColors";
import { Position } from "reactflow";
import { NodeProps, useReactFlow } from "reactflow";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import CustomHandle from "./CustomHandle";
import MessagePanelTemplate from "./MessagePanelTemplate";
import DevicePreviewComponent from "../../TemplateComponents/TemplateForm/devicePreview";
import { unescapeJsonString } from "../../../utils/functions";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { fetchTemplateById } from "../../../redux/slices/Templates/TemplateById";
import { toastActions } from "../../../utils/toastSlice";
import { useWorkflow } from "../../../contexts/WorkflowContext";
import TimerIcon from "@mui/icons-material/Timer";
import { Edge } from "reactflow";

interface TemplateNodeProps {
  data: {
    templateSelectorOpen: boolean;
    isMessagePanelOpen: boolean;
    selectedTemplate: any;
    templateObj: any;
    isValid?: boolean;
    isEnableSetTimeOut: boolean;
    timeoutTime: number;
    timeoutUnit: string;
    timeoutTimeInMinutes: number;
  };
  id: string;
}

const TemplateNode: React.FC<TemplateNodeProps> = ({ data, id }) => {
  const {
    isMessagePanelOpen,
    templateSelectorOpen,
    selectedTemplate,
    templateObj,
    isEnableSetTimeOut,
    timeoutTime,
    timeoutUnit,
    timeoutTimeInMinutes,
  } = data;
  console.log("data", data);

  const { setNodes, handleNodeDelete } = useWorkflow();
  const { getEdges } = useReactFlow();
  const [localTemplate, setLocalTemplate] = useState<any>(null);
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const [currentIndex, setCurrentIndex] = useState(0);

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds: any[]) => {
      const updatedNodes = nds.map((node: any) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Only validate if the node has already been marked as invalid
          // This ensures validation only runs after the Save button has been clicked
          if (updatedData.isValid === false) {
            // Validate the node data to determine if all errors are resolved
            // For template nodes, we need to check if a template is selected
            const isTemplateSelected = !!(
              updatedData.templateObj && updatedData.templateObj.templateId
            );

            // NEW: Check timeout path connection if timeout is enabled
            const timeoutValid =
              !updatedData.isEnableSetTimeOut ||
              isTimeoutConnected(id, getEdges());

            // Only set isValid to true if all validation checks pass
            if (isTemplateSelected && timeoutValid) {
              updatedData.isValid = true;
            }
          }

          return { ...node, data: updatedData };
        }
        return node;
      });
      return updatedNodes;
    });
  };

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };

  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const handleSendTemplatePayload = (payload: any, templateState: any) => {
    // First update the node data
    updateNodeData((data) => ({
      ...data,
      templateObj: templateState,
    }));

    // Then update local state
    setLocalTemplate(templateState);
  };

  async function getTemplateById(id: any) {
    try {
      const payload = {
        templateId: id,
        businessId: userData?.companyId,
        userId: userData?.userId,
      };
      const result = await dispatch(fetchTemplateById(payload));
      if (result?.meta?.requestStatus === "fulfilled") {
        setLocalTemplate(result?.payload?.[0]);
        updateNodeData((data) => ({
          ...data,
          selectedTemplate: result?.payload?.[0],
        }));
      } else {
        dispatch(
          toastActions.setToaster({
            message: result?.payload?.message || "Error fetching template",
            type: "error",
          })
        );
      }
    } catch (error) {
      console.error("Error fetching template:", error);
    }
  }

  // Helper function to check if a timeout handle is connected
  const isTimeoutConnected = (nodeId: string, allEdges: Edge[]) => {
    return allEdges.some(
      (edge) =>
        edge.source === nodeId &&
        edge.sourceHandle &&
        edge.sourceHandle.startsWith(`right-timeoutId-`)
    );
  };

  const formatTimeoutTime = () => {
    if (!timeoutTime || !timeoutUnit) return "No time set";
    return `${timeoutTime} ${
      timeoutUnit === "minutes"
        ? "min"
        : timeoutUnit === "hours"
        ? "hours"
        : timeoutUnit === "days"
        ? "days"
        : "seconds"
    }`;
  };

  const handleTimeoutTimeChange = (time: number, unit: string) => {
    updateNodeData((data) => ({
      ...data,
      timeoutTimeInMinutes:
        time *
        (unit === "minutes"
          ? 1
          : unit === "hours"
          ? 60
          : unit === "days"
          ? 1440
          : 0),
      timeoutTime: time,
      timeoutUnit: unit,
    }));
  };

  const handleSetTimeoutEnabled = (value: boolean) => {
    updateNodeData((data) => ({
      ...data,
      isEnableSetTimeOut: value,
    }));
  };

  useEffect(() => {
    if (templateObj?.templateId) {
      getTemplateById(templateObj?.templateId);
    }
  }, [templateObj]);

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 320,
        minHeight: "200px",
        maxHeight: isEnableSetTimeOut ? "500px" : "400px",
        display: "flex",
        flexDirection: "column",
        cursor: "auto",
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexShrink: 0,
        }}
      >
        <Typography variant="h6">Template</Typography>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>

      <Box
        sx={{
          padding: "12px",
          fontSize: "14px",
          color: "#333",
          flex: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        {data.isValid === false && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
              flexShrink: 0,
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {!data.templateObj?.templateId && (
                <Typography component="li" variant="caption" color="error">
                  Template must be selected
                </Typography>
              )}
            </Box>
            {isEnableSetTimeOut && !isTimeoutConnected(id, getEdges()) && (
              <Box component="ul" sx={{ m: 0, pl: 2 }}>
                <Typography component="li" variant="caption" color="error">
                  Timeout path must be connected to another node
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {localTemplate ? (
          <Box
            sx={{ display: "flex", flexDirection: "column", height: "100%" }}
          >
            <Typography
              variant="body1"
              sx={{
                mb: 2,
                backgroundColor: bgColors.green2,
                color: bgColors.green,
                fontWeight: 600,
                borderRadius: "6px",
                padding: "4px 8px",
                flexShrink: 0,
              }}
            >
              {localTemplate?.templateName}
            </Typography>
            <Box
              sx={{
                width: "100%",
                flex: 1,
                maxHeight: "250px",
                overflowY: "auto",
                overflowX: "hidden",
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  background: "#f1f1f1",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb": {
                  background: "#888",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb:hover": {
                  background: "#555",
                },
                "& > div": {
                  width: "100% !important",
                  height: "auto !important",
                  "& > div": {
                    transform: "scale(0.8)",
                    transformOrigin: "top center",
                  },
                },
              }}
              className="nowheel"
              onWheel={(e) => {
                e.stopPropagation();
                const container = e.currentTarget;
                container.scrollTop += e.deltaY;
              }}
            >
              <DevicePreviewComponent
                header={localTemplate?.header}
                body={unescapeJsonString(localTemplate?.body)}
                footer={localTemplate?.footer}
                mediaType={localTemplate?.mediaType}
                mediaFile={localTemplate?.mediaFile}
                buttons={localTemplate?.buttons}
                carouselCards={localTemplate?.carouselCards}
                currentIndex={currentIndex}
                setCurrentIndex={setCurrentIndex}
                style={{ maxWidth: "100%", width: "100%" }}
              />
            </Box>
          </Box>
        ) : (
          <Typography variant="body1">
            No template selected. Click the edit icon to select a template.
          </Typography>
        )}
      </Box>

      {/* Timeout section moved outside scrollable container */}
      {isEnableSetTimeOut && (
        <Box
          sx={{
            mx: 1,
            backgroundColor: bgColors.green1,
            p: 2,
            borderRadius: 1,
            mb: 2,
            display: "flex",
            flexDirection: "column",
            gap: 1,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <TimerIcon color="success" />
            <Typography variant="body2" fontWeight={600}>
              Set timeout after: {formatTimeoutTime()}
            </Typography>
          </Box>
          <Box sx={{ position: "relative" }}>
            <Button
              variant="outlined"
              sx={{
                background: bgColors.green1,
                borderRadius: 2,
                mt: 1,
                position: "relative",
                color: bgColors.green,
                width: "100%",
                "&:hover": {
                  backgroundColor: bgColors.green1,
                },
              }}
            >
              Add timeout
              <Box
                sx={{
                  position: "absolute",
                  right: -2,
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <CustomHandle
                  type="source"
                  position={Position.Right}
                  id={`right-timeoutId-${id}`}
                />
              </Box>
            </Button>
          </Box>
        </Box>
      )}

      {isMessagePanelOpen && (
        <MessagePanelTemplate
          id={id}
          handleEdit={handleEdit}
          templateObj={templateObj}
          localTemplate={localTemplate}
          setLocalTemplate={setLocalTemplate}
          handleSendTemplatePayload={handleSendTemplatePayload}
          handleSetTimeoutEnabled={handleSetTimeoutEnabled}
          handleTimeoutTimeChange={handleTimeoutTimeChange}
          timeoutTime={timeoutTime}
          timeoutUnit={timeoutUnit}
          isEnableSetTimeOut={isEnableSetTimeOut}
        />
      )}
    </Paper>
  );
};

export default TemplateNode;
