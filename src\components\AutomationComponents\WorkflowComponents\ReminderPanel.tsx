import {
  Box,
  IconButton,
  Paper,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Divider,
} from "@mui/material";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { useReactFlow } from "reactflow";
import TemplatePopUp from "../../InboxComponents/inboxDetailsComponents/TemplatePopUp";
import { useState } from "react";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

interface ReminderPanelProps {
  id: string;
  handleEdit: () => void;
  templateObj: any;
  localTemplate: any;
  setLocalTemplate: (template: any) => void;
  handleSendTemplatePayload: (payload: any, templateState: any) => void;
  reminderTime: number;
  reminderUnit: string;
  handleTimeChange: (time: number, unit: string) => void;
}

const ReminderPanel = ({
  id,
  handleEdit,
  templateObj,
  localTemplate,
  setLocalTemplate,
  handleSendTemplatePayload,
  reminderTime,
  reminderUnit,
  handleTimeChange,
}: ReminderPanelProps) => {
  const { setNodes } = useReactFlow();
  const [templateSelectorOpen, setTemplateSelectorOpen] = useState(false);
  const handleTemplateClick = () => {
    setTemplateSelectorOpen((prev) => !prev);
  };

  const handleTimeInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseInt(event.target.value) || 0;
    handleTimeChange(value, reminderUnit || "minutes");
  };

  const handleUnitChange = (event: any) => {
    const unit = event.target.value;
    handleTimeChange(reminderTime || 1, unit);
  };

  const timeUnits = [
    { value: "minutes", label: "Minutes" },
    { value: "hours", label: "Hours" },
    { value: "days", label: "Days" },
  ];

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -360,
        top: 0,
        width: 350,
        height: 500,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      {/* Header */}
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          height: "50px",
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <AccessTimeIcon sx={{ color: "white" }} />
          <Typography variant="h6" color="white">
            Reminder Settings
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          sx={{
            color: `${bgColors.red}`,
            width: 48,
            height: 48,
            "& svg": {
              width: 40,
              height: 40,
              fill: `${bgColors.red1}`,
            },
          }}
          onClick={() => handleEdit()}
        >
          <CloseIconSvg />
        </IconButton>
      </Box>

      {/* Content */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: 2,
          height: "calc(500px - 50px)",
          display: "flex",
          flexDirection: "column",
          gap: 3,
        }}
      >
        {/* Reminder Time Section */}
        <Box>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Reminder Timing
          </Typography>
          <Box sx={{ display: "flex", gap: 2, alignItems: "flex-end" }}>
            <TextField
              label="Time"
              type="number"
              value={reminderTime || ""}
              onChange={handleTimeInputChange}
              size="small"
              inputProps={{ min: 1 }}
              sx={{
                flex: 1,
                "& .MuiOutlinedInput-root": {
                  borderColor: bgColors.green,
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: bgColors.green,
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: bgColors.green,
                  },
                },
              }}
            />
            <FormControl size="small" sx={{ flex: 1 }}>
              <InputLabel>Unit</InputLabel>
              <Select
                value={reminderUnit || "minutes"}
                onChange={handleUnitChange}
                label="Unit"
                sx={{
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: bgColors.green,
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: bgColors.green,
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: bgColors.green,
                  },
                }}
              >
                {timeUnits.map((unit) => (
                  <MenuItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          {reminderTime && reminderUnit && (
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Reminder will be sent after {reminderTime} {reminderUnit}
            </Typography>
          )}
        </Box>

        <Divider />

        {/* Template Selection Section */}
        <Box>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Template Selection
          </Typography>

          {localTemplate ? (
            <Box
              sx={{
                border: `2px solid ${bgColors.green}`,
                borderRadius: "8px",
                p: 2,
                backgroundColor: bgColors.green1,
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontWeight: 600,
                  color: bgColors.green,
                  mb: 1,
                }}
              >
                Selected Template:
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {localTemplate?.templateName}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: bgColors.green,
                  cursor: "pointer",
                  textDecoration: "underline",
                  "&:hover": {
                    color: bgColors.green,
                  },
                }}
                onClick={handleTemplateClick}
              >
                Change template
              </Typography>
            </Box>
          ) : (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                border: "1px dashed #25D366",
                borderRadius: "4px",
                cursor: "pointer",
                textAlign: "center",
                transition: "all 0.2s",
                "&:hover": {
                  backgroundColor: "rgba(37, 211, 102, 0.05)",
                },
                p: 3,
              }}
              onClick={handleTemplateClick}
            >
              <Typography variant="body2">
                Click here to select a WhatsApp template for the reminder
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {templateSelectorOpen && (
        <TemplatePopUp
          open={templateSelectorOpen}
          handleCloseTemplatePopup={handleTemplateClick}
          setSendTemplatePayload={handleSendTemplatePayload}
        />
      )}
    </Paper>
  );
};

export default ReminderPanel;
