import {
  Box,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  Divider,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { bgColors } from "../../../utils/bgColors";
import SearchIconSvg2 from "../../../assets/svgs/SearchIconSvg2";
import CloseSvg from "../../../assets/svgs/CloseSvg";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { fetchAllTemplatesByCompanyId } from "../../../redux/slices/Templates/AllTemplatesSlice";
import DevicePreviewComponent from "../../TemplateComponents/TemplateForm/devicePreview";
import LoadingComponent from "../../common/LoadingComponent";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";

import {
  extractLeadratVariables,
  extractVariables,
  generatePayload,
  generateCarouselPayload,
  ProcessButtonData,
} from "../../TemplateComponents/TemplateForm/functions";
import { styled } from "@mui/material/styles";
import { debounce } from "lodash";
import {
  parseTextToHtml,
  refactorCarouselCards,
  unescapeJsonString,
} from "../../../utils/functions";
import { useLocation, useNavigate } from "react-router-dom";
import RightArrowSvg from "../../../assets/svgs/RightArrowSvg";
import { BiLeftArrowAlt } from "react-icons/bi";
import { useTheme, useMediaQuery } from "@mui/material";
import { fetchInboxContactDetails } from "../../../redux/slices/Inbox/InboxContactDetails";

const StyledPaper = styled("div")(() => ({
  minWidth: "88%",
  borderRadius: "20px !important",
  backgroundColor: "#fff",
  "&::-webkit-scrollbar": {
    cursor: "pointer",
    width: "8px", // Width of the scrollbar
  },
  "&::-webkit-scrollbar-thumb": {
    cursor: "pointer",
    backgroundColor: "#999999", // Color of the scrollbar thumb
    borderRadius: "6px", // Rounded corners for the scrollbar thumb
    transition: "background-color 0.2s ease-in-out", // Optional: Add a transition effect for smooth hover
  },
}));

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    padding: "8px",
    width: "100%",
    borderRadius: "15px",
  },
  bgContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "space-between",
    borderRadius: "5px",
    height: "100%",
    width: "100%",
  },
  manageContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    fontSize: "14px",
    backgroundColor: "transparent",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "Inter",
      color: "#4B5A5A !important",
      fontWeight: "500 !important",
    },
  },
  messageIconStyles: {
    backgroundColor: "#00934F",
    borderRadius: "14px",
    width: "62px",
    height: "62px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  createTemplateButtonText: {
    display: "flex",
    cursor: "pointer",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    color: "#00934F !important",
    fontSize: "16px !important",
    fontWeight: "Semi Bold !important",
  },
  insertButton: {
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "80px",
    padding: "5px",
    height: "38px",
    textAlign: "center",
    cursor: "pointer",
    alignItems: "center",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  buttonText: {
    cursor: "pointer",
    backgroundColor: "transparent",
    border: "none",
    color: bgColors.green,
  },
  mainBorderStyles: {
    borderRadius: "18px",
    border: `2px solid ${bgColors.gray5}`,
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "500 !important",
    fontSize: "16px !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
    fontSize: "14px !important",
    textAlign: "justify",
  },
  textColor: {
    color: "#4B5A5A !important",
    fontSize: "14px !important",
  },
  variable: {
    color: `${bgColors.black1} !important`,
    fontSize: "18px !important",
  },
  smallScreenContainer: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    overflowY: "auto",
    "&::-webkit-scrollbar": {
      width: "8px",
      cursor: "pointer",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "#999999",
      borderRadius: "6px",
      transition: "background-color 0.2s ease-in-out",
    },
  },
  smallScreenPanel: {
    width: "100%",
    overflow: "visible",
  },
});

interface TemplateState {
  templateId: string;
  templateName: string;
  category: number;
  subCategory: string;
  mediaType: number;
  mediaAwsUrl: any;
  header?: string;
  body: string;
  callButtonName?: string;
  phoneNumber?: string;
  countryCode?: string;
  urlButtonName?: string[];
  redirectUrl?: string[];
  quickReply?: string[];
  footer?: string;
  codeDeliverySetup?: string;
  zeroTapAuthentication?: boolean;
  addSecurityRecommendation?: boolean;
  addExpiryTime?: boolean;
  codeExpiresIn?: number;
  autoFillButtonText?: string;
  copyCodeButtonText?: string;
  messageValidity?: boolean;
  messageValidityPeriod?: number;
  appSetup?: Array<{
    appPackageName: string;
    appSignatureHash: string;
  }>;
  leadratVariables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  buttons: {
    buttonType: string;
    buttonValue: string;
    buttonName?: any; // Optional property
  }[];
  variables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  carouselCards?: Array<{
    mediaUrlType: number;
    headerMediaUrl: string;
    body: string;
    callButtonName?: string;
    countryCode?: string;
    phoneNumber?: string;
    quickReply?: Array<string>;
    urlButtonName?: Array<string>;
    redirectUrl?: Array<string>;
    carouselButtons?: Array<{
      buttonType: string;
      buttonValue: string;
      buttonName?: string;
    }>;
  }>;
  carouselBodyVariables?: Array<Array<any>>;
  carouselLeadratBodyVariables?: Array<Array<any>>;
}

const TemplatePopUp = ({
  contactNumber,
  open,
  handleCloseTemplatePopup,
  setSendTemplatePayload,
}: // selectedTemplate,
// setSelectedTemplate,
any) => {
  // console.log("contactNumber", contactNumber);

  const classes = useStyles();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md")); // md = 900px
  const dispatch = useAppDispatch();
  const tableContainerRef = useRef(null);

  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const contactDetails = useAppSelector(
    (state: any) => state?.inboxContactDetails
  );

  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const manageInboxObject = getuserPermissionData?.inbox;
  const templatesSlice = useAppSelector(
    (state: any) => state?.allTemplatesData
  );

  const activeTemplatesData = templatesSlice?.data?.data;

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [templatesData, setTemplatesData] = useState<any>([]);

  const [searchInput, setSearchInput] = useState("");
  const [pageNumber, setPageNumber] = useState(1);
  const [variableErrors, setVariableErrors] = useState<any>({});
  const [templateTooltip, setTemplateTooltip] = useState(false);
  const [isSelectTemplateOpen, setIsSelectTemplateOpen] = useState(true);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const [previewCarouselIndex, setPreviewCarouselIndex] = useState(0);
  const [templateState, setTemplateState] = useState<TemplateState>({
    templateId: "",
    templateName: "",
    category: 1,
    subCategory: "",
    mediaType: 1,
    mediaAwsUrl: null,
    header: "",
    body: "",
    callButtonName: "",
    phoneNumber: "",
    countryCode: "",
    codeDeliverySetup: "COPY_CODE",
    zeroTapAuthentication: false,
    urlButtonName: [],
    redirectUrl: [],
    quickReply: [],
    footer: "",
    buttons: [],
    variables: [],
    leadratVariables: [],
    appSetup: [{ appPackageName: "", appSignatureHash: "" }],
    addSecurityRecommendation: true,
    addExpiryTime: false,
    codeExpiresIn: 10,

    autoFillButtonText: "Auto fill",
    copyCodeButtonText: "Copy code",
    messageValidity: true,
    messageValidityPeriod: 300,
    carouselCards: [],
    carouselBodyVariables: [],
    carouselLeadratBodyVariables: [],
  });
  console.log("templateState", templateState);

  const navigate = useNavigate();
  const location = useLocation();

  console.log("contactDetails", contactDetails?.data);

  const contactColumnNames = [
    // {
    //   name: "ContactId",
    //   value: contactDetails?.data?.contactId,
    // },
    { name: "Name", value: contactDetails?.data?.name },
    // { name: "BusinessId", value: userData?.companyId },
    // { name: "UserId", value: userData?.userId },
    { name: "ContactNumber", value: contactDetails?.data?.phoneNumber },
    { name: "Email", value: contactDetails?.data?.email },
    { name: "CountryName", value: contactDetails?.data?.countryName },
    // { name: "Tags", value: contactDetails?.data?.tags },
    // { name: "CreatedDate", value: contactDetails?.data?.createdDate },
    // { name: "IsActive", value: contactDetails?.data?.contactExist },
    // { name: "Note", value: contactDetails?.data?.note },
    // { name: "ChatStatus", value: contactDetails?.data?.chatStatus },
    // { name: "IsSpam", value: contactDetails?.data?.isSpam },
  ];

  const [previewOpen, setPreviewOpen] = useState(false);

  // Add refs and sync flag
  const leftPanelRef = useRef<HTMLDivElement>(null);
  const rightPanelRef = useRef<HTMLDivElement>(null);
  const isSyncingScroll = useRef(false);

  // Helper to check if this is a carousel template
  const isCarousel = templateState?.mediaType === 6;
  // Group variables by type (exclude header for carousel)
  const headerVariables: any[] = !isCarousel
    ? templateState?.variables?.filter((v: any) => v.type === "header") || []
    : [];
  console.log("headerVariables", headerVariables);
  const bodyVariables: any[] =
    templateState?.variables?.filter((v: any) => v.type === "body") || [];
  const leadratVariables: any[] = templateState?.leadratVariables || [];
  const carouselBodyVariables: any[] =
    templateState?.carouselBodyVariables || [];
  const carouselLeadratBodyVariables: any[] =
    templateState?.carouselLeadratBodyVariables || [];

  console.log("bodyVariables", bodyVariables);

  const handleLeftScroll = () => {
    if (isSmallScreen) return; // Disable sync on small screens
    if (isSyncingScroll.current) return;
    isSyncingScroll.current = true;
    if (rightPanelRef.current && leftPanelRef.current) {
      rightPanelRef.current.scrollTop = leftPanelRef.current.scrollTop;
    }
    setTimeout(() => {
      isSyncingScroll.current = false;
    }, 0);
  };

  const handleRightScroll = () => {
    if (isSmallScreen) return; // Disable sync on small screens
    if (isSyncingScroll.current) return;
    isSyncingScroll.current = true;
    if (leftPanelRef.current && rightPanelRef.current) {
      leftPanelRef.current.scrollTop = rightPanelRef.current.scrollTop;
    }
    setTimeout(() => {
      isSyncingScroll.current = false;
    }, 0);
  };

  const handleCarouselBodyVariableValueChange = (
    event: any,
    valueType: string,
    variableIndex: number,
    cardIndex: number
  ) => {
    const { value }: any = event.target;

    setTemplateState((prevState: any) => {
      const updatedVariables = prevState?.carouselBodyVariables?.map(
        (card: any, index: any) => {
          if (index === cardIndex) {
            return card.map((variable: any, i: number) => {
              if (i === variableIndex) {
                if (valueType === "value") {
                  const selectedColumn = contactColumnNames.find(
                    (column) => column.name === value
                  ); // Find the corresponding column object
                  return {
                    ...variable,
                    field: value, // Assign the selected field name to the field key
                    value: selectedColumn?.value ?? value, // Assign the value from the selected column or an empty string if not found
                  };
                } else if (valueType === "fallBackValue") {
                  return { ...variable, fallBackValue: value as string };
                }
              }
              return variable;
            });
          }
          return card;
        }
      );

      // When using normal variables, clear leadrat variables
      return {
        ...prevState,
        carouselBodyVariables: updatedVariables,
        carouselLeadratBodyVariables: [], // Clear leadrat variables when using normal variables
      };
    });

    setVariableErrors((prevErrors: any) => {
      const newErrors = { ...prevErrors };
      if (valueType === "value") {
        if (value) {
          if (newErrors[variableIndex]) {
            delete newErrors[variableIndex]?.value;
            if (Object.keys(newErrors[variableIndex]).length === 0) {
              delete newErrors[variableIndex];
            }
          }
        } else {
          newErrors[variableIndex] = {
            ...newErrors[variableIndex],
            value: "Value is required",
          };
        }
      } else if (valueType === "fallBackValue") {
        if (value) {
          if (newErrors[variableIndex]) {
            delete newErrors[variableIndex]?.fallbackValue;
            if (Object.keys(newErrors[variableIndex]).length === 0) {
              delete newErrors[variableIndex];
            }
          }
        } else {
          newErrors[variableIndex] = {
            ...newErrors[variableIndex],
            fallbackValue: "Fallback is required",
          };
        }
      }
      return newErrors;
    });
  };

  const handleCarouselLeadratBodyVariableValueChange = (
    event: any,
    valueType: string,
    variableIndex: number,
    cardIndex: number
  ) => {
    const { value }: any = event.target;

    setTemplateState((prevState: any) => {
      const updatedVariables = prevState?.carouselLeadratBodyVariables?.map(
        (card: any, index: any) => {
          if (index === cardIndex) {
            return card.map((variable: any, i: number) => {
              if (i === variableIndex) {
                if (valueType === "value") {
                  const selectedColumn = contactColumnNames.find(
                    (column) => column.name === value
                  ); // Find the corresponding column object
                  return {
                    ...variable,
                    field: value, // Assign the selected field name to the field key
                    value: selectedColumn?.value ?? value, // Assign the value from the selected column or an empty string if not found
                  };
                } else if (valueType === "fallBackValue") {
                  return { ...variable, fallBackValue: value as string };
                }
              }
              return variable;
            });
          }
          return card;
        }
      );

      // When using leadrat variables, clear normal variables
      return {
        ...prevState,
        carouselLeadratBodyVariables: updatedVariables,
        carouselBodyVariables: [], // Clear normal variables when using leadrat variables
      };
    });

    // For leadrat variables, we only need to validate fallback values
    if (valueType === "fallBackValue") {
      setVariableErrors((prevErrors: any) => {
        const newErrors = { ...prevErrors };
        if (value) {
          if (newErrors[variableIndex]) {
            delete newErrors[variableIndex]?.fallbackValue;
            if (Object.keys(newErrors[variableIndex]).length === 0) {
              delete newErrors[variableIndex];
            }
          }
        } else {
          newErrors[variableIndex] = {
            ...newErrors[variableIndex],
            fallbackValue: "Fallback is required",
          };
        }
        return newErrors;
      });
    }
  };

  const hasTemplatePermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "sendTemplates")) {
        return true;
      }
    }
    return false;
  };

  const handleOpenCreateTemplateDialog = (
    action: string,
    templateId: string
  ) => {
    // setOpenCreateTemplateDialog(true);

    navigate("/templates", { state: { from: location.pathname } });
  };

  const toggleSelectTemplate = (value: any) => {
    setIsSelectTemplateOpen(value);
  };

  const handleSearchChange = (event: any) => {
    const inputValue = event.target.value;
    setPageNumber(1);
    setSearchInput(inputValue);
  };

  const handleChange = (event: any) => {
    if (event.mediaType === 6) {
      setVariableErrors({});
      // Reset carousel index when selecting a new carousel template
      setCarouselIndex(0);

      const carouselData = refactorCarouselCards(
        JSON.parse(event.carouselCardsJson)
      );
      const bodyVariables = extractVariables(event?.body, "body");
      const bodyLeadratVariables = extractLeadratVariables(event?.body, "body");
      let carouselBodyVariables = carouselData.map((card: any) => {
        const bodyVariables = extractVariables(card.body, "carousel");
        return bodyVariables;
      });

      let carouselLeadratBodyVariables = carouselData.map((card: any) => {
        const bodyLeadratVariables = extractLeadratVariables(
          card.body,
          "carousel"
        );
        return bodyLeadratVariables;
      });
      if (carouselLeadratBodyVariables[0].length === 0) {
        carouselLeadratBodyVariables = [];
      }
      if (carouselBodyVariables[0].length === 0) {
        carouselBodyVariables = [];
      }

      const newVariables = [...bodyVariables];
      const newLeadratVariables = [...bodyLeadratVariables];
      setTemplateState({
        templateId: event?.templateId,
        templateName: event?.templateName,
        category: event?.category,
        subCategory: event?.subCategory,
        mediaType: event?.mediaType,
        mediaAwsUrl: event?.mediaAwsUrl,
        header: event?.header,
        body: event?.body,
        variables: newVariables,
        leadratVariables: newLeadratVariables,
        carouselCards: carouselData,
        buttons: [],
        carouselBodyVariables: carouselBodyVariables,
        carouselLeadratBodyVariables: carouselLeadratBodyVariables,
      });
    } else {
      setVariableErrors({});
      const { buttons, ...restTemplateState } = event;
      const { updatedState, quickReply, redirectUrl, urlButtonName } =
        ProcessButtonData(restTemplateState, buttons);

      const bodyVariables = extractVariables(event?.body, "body");
      const headerVariables = extractVariables(event?.header, "header");
      const redirectUrlVariables = extractVariables(
        event?.buttons,
        "url Button"
      );

      const newVariables = [
        ...headerVariables,
        ...bodyVariables,
        ...redirectUrlVariables,
      ];

      const bodyLeadratVariables = extractLeadratVariables(event?.body, "body");
      const headerLeadratVariables = extractLeadratVariables(
        event?.header,
        "header"
      );

      const newLeadratVariables = [
        ...headerLeadratVariables,
        ...bodyLeadratVariables,
      ];

      setTemplateState({
        templateId: event?.templateId,
        templateName: event?.templateName,
        category: event?.category,
        subCategory: event?.subCategory,
        mediaType: event?.mediaType,
        mediaAwsUrl: event?.mediaAwsUrl,
        header: event?.header,
        body: event?.body,
        callButtonName: updatedState?.callButtonName || "",
        phoneNumber: updatedState?.phoneNumber || "",
        countryCode: event?.countryCode,
        urlButtonName: urlButtonName,
        redirectUrl: redirectUrl,
        codeDeliverySetup: event?.codeDeliverySetup,
        zeroTapAuthentication: event?.zeroTapAuthentication,
        appSetup: event?.appSetup,
        addSecurityRecommendation: event?.addSecurityRecommendation,
        addExpiryTime: event?.addExpiryTime,
        codeExpiresIn: event?.codeExpiresIn,
        quickReply: quickReply,
        footer: event?.footer,
        buttons: event?.buttons,
        variables: newVariables,
        leadratVariables: newLeadratVariables,

        autoFillButtonText: event?.autoFillButtonText,
        copyCodeButtonText: event?.copyCodeButtonText,
        messageValidity: event?.messageValidity,
        messageValidityPeriod: event?.messageValidityPeriod,
        carouselCards: event?.carouselCards,
        carouselBodyVariables: [],
        carouselLeadratBodyVariables: [],
      });
    }

    setIsSelectTemplateOpen(false);
    // setSelectedTemplate(templateState);
  };

  const handleVariableValueChange = (
    event: React.ChangeEvent<{ value: unknown }>,
    valueType: string,
    variableIndex: number,
    variableType: string
  ) => {
    const { value }: any = event.target;

    setTemplateState((prevState: TemplateState) => {
      // Find the actual variable in the array based on type and index
      const actualIndex = prevState.variables.findIndex(
        (v, idx) =>
          v.type === variableType &&
          prevState.variables
            .filter((v2) => v2.type === variableType)
            .indexOf(v) === variableIndex
      );

      if (actualIndex === -1) return prevState;

      const updatedVariables = prevState.variables.map(
        (variable: any, index: number) => {
          if (index === actualIndex) {
            if (valueType === "value") {
              const selectedColumn = contactColumnNames.find(
                (column: { name: string; value: string }) =>
                  column.name === value
              );
              return {
                ...variable,
                field: value,
                value: selectedColumn?.value ?? value,
              };
            } else if (valueType === "fallBackValue") {
              return { ...variable, fallBackValue: value as string };
            }
          }
          return variable;
        }
      );

      return {
        ...prevState,
        variables: updatedVariables,
      };
    });
    setVariableErrors((prevErrors: any) => {
      const newErrors = { ...prevErrors };
      if (valueType === "value") {
        if (value) {
          if (newErrors[variableIndex]) {
            delete newErrors[variableIndex]?.value;
            if (Object.keys(newErrors[variableIndex]).length === 0) {
              delete newErrors[variableIndex];
            }
          }
        } else {
          newErrors[variableIndex] = {
            ...newErrors[variableIndex],
            value: "Value is required",
          };
        }
      } else if (valueType === "fallBackValue") {
        if (value) {
          if (newErrors[variableIndex]) {
            delete newErrors[variableIndex]?.fallbackValue;
            if (Object.keys(newErrors[variableIndex]).length === 0) {
              delete newErrors[variableIndex];
            }
          }
        } else {
          newErrors[variableIndex] = {
            ...newErrors[variableIndex],
            fallbackValue: "Fallback is required",
          };
        }
      }
      return newErrors;
    });
  };

  const handleLeadratVariableValueChange = (
    event: React.ChangeEvent<{ value: unknown }>,
    valueType: string,
    variableIndex: number,
    variableType: string
  ) => {
    const { value }: any = event.target;

    setTemplateState((prevState: TemplateState) => {
      // Find the actual variable in the array based on type and index
      const actualIndex = prevState.leadratVariables.findIndex(
        (v, idx) =>
          v.type === variableType &&
          prevState.leadratVariables
            .filter((v2) => v2.type === variableType)
            .indexOf(v) === variableIndex
      );

      if (actualIndex === -1) return prevState;

      const updatedVariables = prevState.leadratVariables.map(
        (variable: any, index: number) => {
          if (index === actualIndex) {
            if (valueType === "value") {
              const selectedColumn = contactColumnNames.find(
                (column: { name: string; value: string }) =>
                  column.name === value
              );
              return {
                ...variable,
                field: value,
                value: selectedColumn?.value ?? value,
              };
            } else if (valueType === "fallBackValue") {
              return { ...variable, fallBackValue: value as string };
            }
          }
          return variable;
        }
      );

      return {
        ...prevState,
        leadratVariables: updatedVariables,
      };
    });
    setVariableErrors((prevErrors: any) => {
      const newErrors = { ...prevErrors };
      const errorKey = variableType === "header"
        ? `leadrat_header_${variableIndex}`
        : `leadrat_body_${variableIndex}`;
      if (valueType === "value") {
        if (value) {
          if (newErrors[errorKey]) {
            delete newErrors[errorKey].value;
            if (Object.keys(newErrors[errorKey]).length === 0) {
              delete newErrors[errorKey];
            }
          }
        } else {
          newErrors[errorKey] = {
            ...newErrors[errorKey],
            value: "Value is required",
          };
        }
      } else if (valueType === "fallBackValue") {
        if (value) {
          if (newErrors[errorKey]) {
            delete newErrors[errorKey].fallbackValue;
            if (Object.keys(newErrors[errorKey]).length === 0) {
              delete newErrors[errorKey];
            }
          }
        } else {
          newErrors[errorKey] = {
            ...newErrors[errorKey],
            fallbackValue: "Fallback is required",
          };
        }
      }
      return newErrors;
    });
  };

  const handlePreview = () => {
    if (!handleValidityVariableValues()) {
      return;
    }
    // Get current template state
    const bodyText = templateState?.body || "";
    const headerText = templateState?.header || "";

    // Get the latest variables based on their type
    const latestBodyVariables = templateState?.variables?.filter(
      (variable) => variable?.type === "body"
    );

    const latestHeaderVariables = templateState?.variables?.filter(
      (variable) => variable?.type === "header"
    );

    const latestUrlButtonVariables = templateState?.variables?.filter(
      (variable) => variable?.type === "url Button"
    );

    // Replace variables in body text
    let updatedBodyText = bodyText;

    latestBodyVariables?.forEach((variable) => {
      updatedBodyText = updatedBodyText?.replace(
        variable?.id,
        variable?.value ? variable?.value : variable?.fallBackValue
      );
    });

    // Replace variables in header text
    let updatedHeaderText = headerText;
    latestHeaderVariables.forEach((variable) => {
      updatedHeaderText = updatedHeaderText?.replace(
        variable?.id,
        variable?.value ? variable?.value : variable?.fallBackValue
      );
    });

    // Replace variables in URL button values
    let updatedButtons = templateState?.buttons?.map((button: any) => {
      if (button?.buttonType === "URL") {
        let updatedButtonValue = button?.buttonValue;
        latestUrlButtonVariables.forEach((variable) => {
          updatedButtonValue = updatedButtonValue?.replace(
            variable.id,
            variable.value ? variable.value : variable.fallBackValue
          );
        });
        return {
          ...button,
          buttonValue: updatedButtonValue,
        };
      }
      return button;
    });

    // Get the latest variables based on their type
    const latestLeadratBodyVariables = templateState?.leadratVariables?.filter(
      (variable) => variable?.type === "body"
    );

    const latestLeadratHeaderVariables =
      templateState?.leadratVariables?.filter(
        (variable) => variable?.type === "header"
      );

    latestLeadratBodyVariables?.forEach((variable) => {
      updatedBodyText = updatedBodyText?.replace(
        variable?.id,
        variable?.value ? variable?.value : variable?.fallBackValue
      );
    });
    latestLeadratHeaderVariables.forEach((variable) => {
      updatedHeaderText = updatedHeaderText?.replace(
        variable?.id,
        variable?.value ? variable?.value : variable?.fallBackValue
      );
    });

    let updatedCarouselCards = templateState?.carouselCards?.map(
      (card: any, cardIndex: number) => {
        const bodyVariables = templateState?.carouselBodyVariables?.[cardIndex];
        const leadratBodyVariables =
          templateState?.carouselLeadratBodyVariables?.[cardIndex];
        bodyVariables?.forEach((variable: any, variableIndex: number) => {
          card.body = card.body.replace(
            variable.id,
            variable.value ? variable.value : variable.fallBackValue
          );
        });
        leadratBodyVariables?.forEach(
          (variable: any, variableIndex: number) => {
            card.body = card.body.replace(
              variable.id,
              variable.value ? variable.value : variable.fallBackValue
            );
          }
        );
        return card;
      }
    );

    // Update template state with replaced texts
    let updatedState = {
      ...templateState,
      body: updatedBodyText,
      header: updatedHeaderText,
      buttons: updatedButtons,
      carouselCards: updatedCarouselCards,
    };
    setTemplateState((prevState) => {
      updatedState = {
        ...prevState,
        body: updatedBodyText,
        header: updatedHeaderText,
        buttons: updatedButtons,
        carouselCards: updatedCarouselCards,
      };
      return updatedState;
    });
    setPreviewOpen(true);
    return updatedState;
  };

  const handleValidityVariableValues = () => {
    const errors: any = {};

    templateState?.variables?.forEach((variable, index) => {
      // Only check the value field if the type is not 'url Button'
      if (variable.type !== "url Button" && !variable?.field) {
        errors[index] = { ...errors[index], value: "Value is required" };
      }

      // Check fallBackValue for all types
      if (!variable?.fallBackValue) {
        errors[index] = {
          ...errors[index],
          fallbackValue: "Fallback is required",
        };
      }
    });

    // Validate leadrat header variables
    const headerLeadratVars = templateState?.leadratVariables?.filter(v => v.type === "header") || [];
    headerLeadratVars.forEach((variable, idx) => {
      const key = `leadrat_header_${idx}`;
      if (!variable?.field) {
        errors[key] = { ...errors[key], value: "Value is required" };
      }
      if (!variable?.fallBackValue) {
        errors[key] = { ...errors[key], fallbackValue: "Fallback is required" };
      }
    });
    // Validate leadrat body variables
    const bodyLeadratVars = templateState?.leadratVariables?.filter(v => v.type === "body") || [];
    bodyLeadratVars.forEach((variable, idx) => {
      const key = `leadrat_body_${idx}`;
      if (!variable?.field) {
        errors[key] = { ...errors[key], value: "Value is required" };
      }
      if (!variable?.fallBackValue) {
        errors[key] = { ...errors[key], fallbackValue: "Fallback is required" };
      }
    });

    setVariableErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Function to handle inserting the template
  const handleInsertTemplate = () => {
    console.log("Insert Template clicked");
    
    const hasPermission = hasTemplatePermission(manageInboxObject);
    if (hasPermission) {
      // Validate variables first
      if (!handleValidityVariableValues()) {
        return;
      }

      // Generate payload directly from the current template state
      let payload;
      if (templateState?.mediaType === 6) {
        payload = generateCarouselPayload(
          userData,
          contactNumber,
          templateState
        );
      } else {
        payload = generatePayload(userData, contactNumber, templateState);
      }

      if (payload?.templateId) {
        setSendTemplatePayload(payload, templateState);
        setVariableErrors({});
        setCarouselIndex(0);
        setTemplateState({
          templateId: "",
          templateName: "",
          category: 1,
          subCategory: "",
          mediaType: 1,
          mediaAwsUrl: null,
          header: "",
          body: "",
          callButtonName: "",
          phoneNumber: "",
          countryCode: "",
          urlButtonName: [],
          redirectUrl: [],
          quickReply: [],
          codeDeliverySetup: "COPY_CODE",
          zeroTapAuthentication: false,
          appSetup: [{ appPackageName: "", appSignatureHash: "" }],
          addSecurityRecommendation: true,
          addExpiryTime: false,
          codeExpiresIn: 10,
          autoFillButtonText: "Auto fill",
          copyCodeButtonText: "Copy code",
          messageValidity: true,
          messageValidityPeriod: 300,
          footer: "",
          buttons: [],
          variables: [],
          leadratVariables: [],
          carouselCards: [],
          carouselBodyVariables: [],
          carouselLeadratBodyVariables: [],
        });
        // Close the dialog after successful template insertion
        handleCloseTemplatePopup();
        setIsSelectTemplateOpen(true);
      }
    } else {
      setTemplateTooltip(true);
      setTimeout(() => {
        setTemplateTooltip(false);
      }, 2000);
    }
  };

  useEffect(() => {
    const postData = {
      userId: userData?.userId,
      businessId: userData?.companyId,
      pageNumber: pageNumber,
      per_page: 40,
      filters: {
        searching: {
          value: searchInput,
        },
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column: "Status",
              operator: "equal",
              value: "2",
            },
          ],
        },
      },
    };
    dispatch(fetchAllTemplatesByCompanyId(postData));
  }, [dispatch, searchInput, pageNumber]);

  useEffect(() => {
    setTemplatesData(templatesSlice?.data?.data);
  }, []);

  const handleScroll = useCallback(
    debounce(() => {
      if (tableContainerRef?.current) {
        const { scrollTop, scrollHeight, clientHeight } =
          tableContainerRef.current;

        if (
          scrollTop + clientHeight >= scrollHeight - 10 &&
          !isLoadingMore &&
          templatesData?.length !== templatesSlice?.data?.total
        ) {
          handleLoadMore();
        }
      }
    }, 200),
    [isLoadingMore, templatesData?.length, templatesSlice?.data?.total]
  );
  const handleLoadMore = useCallback(() => {
    if (
      !isLoadingMore &&
      templatesData?.length !== templatesSlice?.data?.total &&
      pageNumber <= Math.ceil(templatesSlice?.data?.total) / 40
    ) {
      setIsLoadingMore(true);
      setPageNumber((prevPage) => prevPage + 1);
    }
  }, [isLoadingMore, templatesData?.length, templatesSlice?.data?.total]);

  useEffect(() => {
    if (activeTemplatesData) {
      if (pageNumber === 1) {
        setTemplatesData(activeTemplatesData);
      } else {
        setTemplatesData((prevData: any) => [
          ...prevData,
          ...(activeTemplatesData || []),
        ]);
      }
      setIsLoadingMore(false);
    }
  }, [activeTemplatesData]);

  useEffect(() => {
    if (
      tableContainerRef?.current &&
      templatesData?.filter((template: any) => template.category !== 3)
        ?.length < templatesSlice?.data?.total
    ) {
      const { scrollHeight, clientHeight } = tableContainerRef.current;
      if (scrollHeight <= clientHeight && !isLoadingMore) {
        handleLoadMore();
      }
    }
  }, [
    templatesData,
    templatesSlice?.data?.total,
    isLoadingMore,
    handleLoadMore,
  ]);

  useEffect(() => {
    if (
      templatesData?.filter((template: any) => template.category !== 3)
        ?.length < templatesData?.length
    ) {
      handleLoadMore();
    }
  }, [templatesData]);

  useEffect(() => {
    if (
      templatesData?.filter((template: any) => template.category !== 3)
        ?.length < templatesData?.length
    ) {
      handleLoadMore();
    }
  }, [templatesData]);

  // useEffect(() => {
  //   if (contactNumber !== "help") {
  //     fetchContactDetails();
  //   }
  // }, [contactNumber]);

  // const fetchContactDetails = async () => {
  //   const data = {
  //     businessId: userData?.companyId,
  //     contactId: contactNumber,
  //   };
  //   const response = await dispatch(fetchInboxContactDetails(data));
  //   console.log("response", response);
  // }

  const PreviewDialog = () => (
    <Dialog
      open={previewOpen}
      onClose={() => setPreviewOpen(false)}
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxWidth: 420,
          width: "100%",
          overflow: "hidden",
        },
      }}
    >
      <DialogTitle sx={{ p: 0, m: 0, px: 2, pt: 2 }}>
        <Box display="flex" justifyContent="flex-end" alignItems="center">
          {/* <Typography variant="h6">Preview</Typography> */}
          <IconButton onClick={() => setPreviewOpen(false)}>
            <CloseSvg />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          p: 0,
          minWidth: 320,
          maxWidth: 400,
          width: "100%",
          height: "100%",
          overflow: "hidden",
          pb: 2, // Add padding-bottom for spacing above buttons
        }}
      >
        <Box
          sx={{
            width: "100%",
            maxWidth: 340,
            minWidth: 300,
            height: 560,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mx: "auto",
            p: 0,
            background: "#f8fafc",
            borderRadius: 2,
            mb: 2,
          }}
        >
          <DevicePreviewComponent
            header={templateState?.header}
            body={formatBodyText(unescapeJsonString(templateState?.body))}
            footer={templateState?.footer}
            mediaType={templateState?.mediaType}
            mediaFile={
              templateState?.mediaAwsUrl ? templateState?.mediaAwsUrl : null
            }
            buttons={templateState?.buttons}
            style={{ maxWidth: "340px" }}
            carouselCards={templateState?.carouselCards?.map((card: any) => ({
              ...card,
              body: formatBodyText(unescapeJsonString(card.body)),
            }))}
            currentIndex={previewCarouselIndex}
            setCurrentIndex={setPreviewCarouselIndex}
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
  // Utility to convert *bold* and \n to HTML and highlight variables
  function formatBodyText(text: string) {
    if (!text) return "";

    // First, escape any existing HTML in the text
    let formatted = text.replace(/[<>&]/g, (char) => {
      switch (char) {
        case "<":
          return "&lt;";
        case ">":
          return "&gt;";
        case "&":
          return "&amp;";
        default:
          return char;
      }
    });

    // Replace variables with highlighted spans
    formatted = formatted.replace(/(\{\{\d+\}\}|#[^#]+#)/g, (match) => {
      return `<span style="color: #00934F; font-weight: 600;">${match}</span>`;
    });

    // Replace *text* with <b>text</b>
    formatted = formatted.replace(/\*([^*]+)\*/g, "<b>$1</b>");

    // Replace \n with <br>
    formatted = formatted.replace(/\\n/g, "<br />");

    return formatted;
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleCloseTemplatePopup();
          setVariableErrors({});
          setIsSelectTemplateOpen(true);
        }}
        // fullScreen // Removed to allow content-based sizing
        PaperProps={{
          component: StyledPaper,
          sx: {
            display: "flex",
            flexDirection: "column",
            maxHeight: "91vh", // Added to limit dialog height
          },
        }}
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              variant="h6"
              style={{
                color: "#000000",
                fontWeight: "600",
                fontSize: "18px",
                paddingLeft: "5px",
              }}
              sx={{ display: { xs: "block", sm: "block" } }}
            >
              {templateState?.templateId ? (
                <>
                  Selected Template:{" "}
                  <span
                    style={{
                      color: "#00934F",
                      fontWeight: "600",
                      fontSize: "18px",
                      paddingLeft: "5px",
                    }}
                  >
                    {templateState?.templateName}
                  </span>
                </>
              ) : (
                "Select Template"
              )}
            </Typography>
            <Box
              onClick={() => {
                setTemplateState({
                  templateId: "",
                  templateName: "",
                  category: 1,
                  subCategory: "",
                  mediaType: 1,
                  mediaAwsUrl: null,
                  header: "",
                  body: "",
                  callButtonName: "",
                  phoneNumber: "",
                  countryCode: "",
                  urlButtonName: [],
                  redirectUrl: [],
                  quickReply: [],
                  footer: "",
                  codeDeliverySetup: "COPY_CODE",
                  zeroTapAuthentication: false,
                  appSetup: [{ appPackageName: "", appSignatureHash: "" }],
                  addSecurityRecommendation: true,
                  addExpiryTime: false,
                  codeExpiresIn: 10,

                  autoFillButtonText: "Auto fill",
                  copyCodeButtonText: "Copy code",
                  messageValidity: true,
                  messageValidityPeriod: 300,
                  buttons: [],
                  variables: [],
                  leadratVariables: [],
                  carouselCards: [],
                });
                setVariableErrors({});
                setCarouselIndex(0);
                handleCloseTemplatePopup();
                setIsSelectTemplateOpen(true);
              }}
              style={{ cursor: "pointer", marginLeft: "60px" }}
            >
              <CloseSvg />
            </Box>
          </Box>
        </DialogTitle>
        <Divider />
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: 0,
            position: "relative",
            overflowY: "auto",
          }}
        >
          {isSelectTemplateOpen ? (
            <Grid
              sx={{
                display: "flex",
                flexDirection: "column",
                position: "absolute",
                top: 0,
                left: 1,
                height: "100%",
                backgroundColor: "#ffffff",
                zIndex: 100,
              }}
            >
              {/* <Grid>
                <Typography
                  variant="h6"
                  style={{
                    color: "#000000",
                    fontWeight: "600",
                    fontSize: "18px",
                    paddingLeft: "25px",
                  }}
                >
                  Select Template
                </Typography>
              </Grid> */}

              <Grid
                className={classes.mainContainer}
                sx={{
                  overflowY: "hidden",
                  flexGrow: 1,
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                {/* <Grid className={classes.bgContainer}> */}
                {/* <Typography
                  mt={4}
                  ml={2}
                  className={classes.createTemplateButtonText}
                  variant="body2"
                  onClick={() => handleOpenCreateTemplateDialog("add", "")}
                >
                  + Create Template
                </Typography> */}
                <Box className={classes.bgContainer} sx={{ flexGrow: 1 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <IconButton
                      color="inherit"
                      aria-label="open drawer"
                      onClick={() => setIsSelectTemplateOpen(false)}
                      edge="start"
                      // sx={{ mr: 2, ...(open && { display: "none" }) }}
                      sx={{
                        // display: { xs: "flex", md: "none" },

                        left: 0,
                        top: 0,
                      }}
                    >
                      <BiLeftArrowAlt />
                    </IconButton>
                    <Box
                      sx={{
                        padding: "8px 16px",
                      }}
                      className={classes.spaceBetween}
                    >
                      <TextField
                        className={classes.searchField}
                        variant="standard"
                        size="small"
                        fullWidth
                        InputProps={{
                          disableUnderline: true,
                          style: {
                            padding: "18px",
                            fontSize: "12px",
                            border: "1px solid rgb(204, 204, 204)",
                            borderRadius: "10px",
                            // fontWeight: "600 !important",
                            height: "38px",
                          },
                          startAdornment: (
                            <IconButton sx={{ p: 0, paddingTop: "3px" }}>
                              <SearchIconSvg2 />
                            </IconButton>
                          ),
                        }}
                        inputProps={{
                          style: {
                            // Additional style for placeholder
                            // fontWeight: "600 !important",
                            padding: "0px", // Apply font weight here
                          },
                        }}
                        onChange={handleSearchChange}
                        placeholder="Search templates"
                      />
                    </Box>
                  </Box>
                  {/* {templatesSlice?.status === "loading" ? (
                    <LoadingComponent height="100%" color={bgColors?.blue} />
                  ) : ( */}
                  <Box
                    sx={{
                      height: "calc(100vh - 15%)",
                      overflowY: "scroll",
                      position: "relative",
                      scrollBehavior: "smooth",
                      // Scrollbar styling
                      "&::-webkit-scrollbar": {
                        cursor: "pointer",
                        width: "6px",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        cursor: "pointer",
                        backgroundColor: "#cdcdcd",
                        borderRadius: "8px",
                        transition: "background-color 0.2s ease-in-out",
                      },
                    }}
                    ref={tableContainerRef}
                    onScroll={handleScroll}
                  >
                    {templatesData?.length > 0 ? (
                      templatesData
                        ?.filter((template: any) => template?.category !== 3)
                        ?.map((template: any) => (
                          <Box
                            className={classes.manageContainer}
                            // style={{ cursor: "pointer" }}
                            sx={{
                              margin: "0px 16px",
                              cursor: "pointer",
                              borderRadius: "8px",
                              background:
                                templateState?.templateId ===
                                template?.templateId
                                  ? bgColors?.gray2
                                  : "transparent",
                              "&:hover": {
                                background: bgColors?.gray2,
                                // borderRadius: "8px",
                              },
                            }}
                            key={template?.templateId}
                            onClick={() => handleChange(template)}
                          >
                            <Typography
                              variant="h6"
                              p={1}
                              className={classes.blackColor}
                              style={{ fontSize: "14px" }}
                            >
                              {template?.templateName}
                            </Typography>
                          </Box>
                        ))
                    ) : (
                      <Typography
                        style={{
                          paddingLeft: "40px",
                          paddingTop: "20px",
                          fontSize: "14px",
                        }}
                      >
                        No Templates Found
                      </Typography>
                    )}
                  </Box>
                  {/* )} */}
                  {/* <Box mt={2}>
                    <PaginationComponent
                      total={templatesSlice?.data?.total}
                      setPage={setPageNumber}
                    />
                  </Box> */}
                  {templatesSlice?.status === "loading" && (
                    <LoadingComponent height="20%" color={bgColors?.blue} />
                  )}
                  {/* <div style={{ height: '20px', margin: '10px 0' }}>
                    <LoadingComponent height="100%" color={bgColors?.blue} />
                  </div> */}
                </Box>
                {/* </Grid> */}
              </Grid>
            </Grid>
          ) : (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              onClick={() => setIsSelectTemplateOpen(true)}
              edge="start"
              // sx={{ mr: 2, ...(open && { display: "none" }) }}
              sx={{
                // display: { xs: "flex", md: "none" },
                position: "absolute",
                left: 0,
                top: 0,
                background: "#cdcdcd",
              }}
            >
              <RightArrowSvg />
            </IconButton>
          )}

          {/* Equal width panels using Grid */}
          <Box
            sx={{
              display: "flex",
              flexDirection: isSmallScreen ? "column" : "row",
              width: "100%",
              height: isSmallScreen ? "auto" : "70vh",
              minHeight: isSmallScreen ? 400 : undefined,
              ...(isSmallScreen && {
                overflowY: "auto",
                "&::-webkit-scrollbar": {
                  width: "8px",
                  cursor: "pointer",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#999999",
                  borderRadius: "6px",
                  transition: "background-color 0.2s ease-in-out",
                },
              }),
            }}
          >
            {/* Left Panel */}
            <Box
              ref={leftPanelRef}
              onScroll={handleLeftScroll}
              sx={{
                flex: 1,
                overflowY: isSmallScreen ? "visible" : "auto",
                height: isSmallScreen ? "auto" : "100%",
                pr: isSmallScreen ? 0 : 2,
                mb: isSmallScreen ? 2 : 0,
                width: isSmallScreen ? "100%" : undefined,
                minWidth: 0,
                ...(isSmallScreen
                  ? {
                      scrollbarWidth: "none",
                      "&::-webkit-scrollbar": { display: "none" },
                    }
                  : {
                      scrollbarWidth: "none",
                      "&::-webkit-scrollbar": { display: "none" },
                      msOverflowStyle: "none",
                    }),
              }}
            >
              {templateState?.templateId ? (
                <Card>
                  {/* <CardHeader
                    title={
                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontWeight: 700,
                            color: "#00934F",
                            letterSpacing: 0.5,
                            fontSize: { xs: 22, sm: 26, md: 28 },
                            lineHeight: 1.2,
                            ml: 2,
                          }}
                        >
                          {templateState?.templateName}
                        </Typography>
                      </Box>
                    }
                  /> */}
                  <CardContent sx={{ ml: 2 }}>
                    {/* Header Section (only if not carousel) */}
                    {!isCarousel && (
                      <>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 600, mb: 1 }}
                        >
                          Header
                        </Typography>
                        {templateState?.mediaType === 3 ? (
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: 200,
                              mb: 2,
                            }}
                          >
                            <img
                              style={{
                                width: "100%",
                                maxWidth: 200,
                                maxHeight: 200,
                                height: "auto",
                                objectFit: "contain",
                                borderRadius: 8,
                                border: "1px solid #d1d5db",
                                background: "#f8fafc",
                              }}
                              src={templateState?.mediaAwsUrl}
                              alt="Header Media"
                            />
                          </Box>
                        ) : templateState?.mediaType === 4 ? (
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: 200,
                              mb: 2,
                              border: "1px solid #d1d5db",
                              borderRadius: 2,
                              background: "#f8fafc",
                              overflow: "hidden",
                            }}
                          >
                            <video
                              style={{
                                width: "100%",
                                maxWidth: 200,
                                maxHeight: 200,
                                height: "auto",
                                objectFit: "contain",
                                background: "#f8fafc",
                                display: "block",
                              }}
                              src={templateState?.mediaAwsUrl}
                              controls
                            />
                          </Box>
                        ) : templateState?.mediaType === 5 ? (
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: 200,
                              mb: 2,
                            }}
                          >
                            <iframe
                              style={{
                                width: "100%",
                                maxWidth: 200,
                                maxHeight: 200,
                                height: "auto",
                                objectFit: "contain",
                                borderRadius: 8,
                                border: "1px solid #d1d5db",
                                background: "#f8fafc",
                              }}
                              src={templateState?.mediaAwsUrl}
                              title="Document"
                            />
                          </Box>
                        ) : (
                          <Typography
                            variant="body1"
                            sx={{
                              background: "#f8fafc",
                              borderRadius: 1,
                              fontSize: 15,
                              fontWeight: 400,
                              color: "#222",
                              padding: "6px 10px",
                              border: "1px solid #d1d5db",
                              boxSizing: "border-box",
                              minHeight: 36,
                              mb: 2,
                              whiteSpace: "pre-line",
                            }}
                          >
                            {String(templateState?.header || "-- No Header --")
                              .split(/(\{\{\d+\}\}|#[^#]+#)/g)
                              .map((part, i) =>
                                /\{\{\d+\}\}/.test(part) ||
                                /^#.+#$/.test(part) ? (
                                  <Box
                                    component="span"
                                    key={i}
                                    sx={{ color: "#00934F", fontWeight: 600 }}
                                  >
                                    {part}
                                  </Box>
                                ) : (
                                  <Box component="span" key={i}>
                                    {part}
                                  </Box>
                                )
                              )}
                          </Typography>
                        )}
                      </>
                    )}

                    {/* Body Section */}
                    <Typography
                      variant="subtitle1"
                      sx={{ fontWeight: 600, mb: 1 }}
                    >
                      Body
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        background: "#f8fafc",
                        borderRadius: 1,
                        fontSize: 15,
                        fontWeight: 400,
                        color: "#222",
                        padding: "6px 10px",
                        border: "1px solid #d1d5db",
                        boxSizing: "border-box",
                        minHeight: 36,
                        mb: 2,
                        whiteSpace: "pre-line",
                      }}
                      dangerouslySetInnerHTML={{
                        __html: formatBodyText(
                          unescapeJsonString(
                            templateState?.body || "-- No Body --"
                          )
                        ),
                      }}
                    />
                    {/* Buttons Section (like header/body/footer) */}
                    {Array.isArray(templateState?.buttons) &&
                      templateState.buttons.length > 0 && (
                        <>
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 600, mb: 1 }}
                          >
                            Buttons
                          </Typography>
                          <Box
                            sx={{
                              background: "#f8fafc",
                              borderRadius: 1,
                              fontSize: 15,
                              fontWeight: 400,
                              color: "#222",
                              padding: "6px 10px",
                              border: "1px solid #d1d5db",
                              boxSizing: "border-box",
                              minHeight: 36,
                              mb: 2,
                            }}
                          >
                            {templateState.buttons.map(
                              (btn: any, bIdx: number) => (
                                <Typography
                                  key={bIdx}
                                  variant="body2"
                                  sx={{ display: "block", mb: 0.5 }}
                                >
                                  <Box
                                    component="span"
                                    sx={{ color: "#222", fontWeight: 400 }}
                                  >
                                    {btn.buttonType === "PHONE_NUMBER"
                                      ? "Phone Number"
                                      : btn.buttonType === "QUICK_REPLY"
                                      ? "Quick Reply"
                                      : btn.buttonType === "URL"
                                      ? "URL"
                                      : btn.buttonType}
                                  </Box>
                                  {": "}
                                  <Box
                                    component="span"
                                    sx={{ color: "#00934F", fontWeight: 600 }}
                                  >
                                    {btn.buttonValue}
                                  </Box>
                                </Typography>
                              )
                            )}
                          </Box>
                        </>
                      )}

                    {/* Footer Section (always at the end) */}
                    <Typography
                      variant="subtitle1"
                      sx={{ fontWeight: 600, mb: 1, mt: 2 }}
                    >
                      Footer
                    </Typography>
                    <Box
                      sx={{
                        background: "#f8fafc",
                        borderRadius: 1,
                        fontSize: 15,
                        fontWeight: 400,
                        color: "#222",
                        padding: "6px 10px",
                        border: "1px solid #d1d5db",
                        boxSizing: "border-box",
                        minHeight: 36,
                        mb: 2,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {templateState?.footer || "-- No Footer --"}
                    </Box>

                    {/* Carousel Cards Preview (for mediaType 6) */}
                    {templateState?.mediaType === 6 &&
                      Array.isArray(templateState?.carouselCards) &&
                      templateState.carouselCards.length > 0 && (
                        <Box sx={{ mt: 3 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 600, mb: 2 }}
                          >
                            Carousel Cards
                          </Typography>
                          <Box
                            sx={{
                              position: "relative",
                              width: "100%",
                              minHeight: 220,
                              overflow: "hidden",
                            }}
                          >
                            {/* Navigation Arrows */}
                            <IconButton
                              onClick={() =>
                                setCarouselIndex((prev) =>
                                  Math.max(prev - 1, 0)
                                )
                              }
                              disabled={carouselIndex === 0}
                              sx={{
                                position: "absolute",
                                left: 0,
                                top: "50%",
                                zIndex: 2,
                                transform: "translateY(-50%)",
                              }}
                            >
                              <span className="material-icons">
                                chevron_left
                              </span>
                            </IconButton>
                            <IconButton
                              onClick={() =>
                                setCarouselIndex((prev) =>
                                  Math.min(
                                    prev + 1,
                                    (templateState.carouselCards?.length || 0) -
                                      1
                                  )
                                )
                              }
                              disabled={
                                carouselIndex ===
                                (templateState.carouselCards?.length || 0) - 1
                              }
                              sx={{
                                position: "absolute",
                                right: 0,
                                top: "50%",
                                zIndex: 2,
                                transform: "translateY(-50%)",
                              }}
                            >
                              <span className="material-icons">
                                chevron_right
                              </span>
                            </IconButton>
                            {/* Sliding Cards Row */}
                            <Box
                              sx={{
                                display: "flex",
                                transition: "transform 0.3s ease",
                                transform: `translateX(-${
                                  carouselIndex * 100
                                }%)`,
                              }}
                            >
                              {templateState.carouselCards.map(
                                (card: any, index: number) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      flex: "0 0 100%",
                                      padding: "0 16px",
                                    }}
                                  >
                                    <Card
                                      sx={{
                                        mx: 6,
                                        background: "#f8fafc",
                                        boxShadow: 0,
                                        border: "1px solid #e0e0e0",
                                      }}
                                    >
                                      <CardContent>
                                        {/* Card Image */}
                                        {card.headerMediaUrl && (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              alignItems: "center",
                                              height: 200,
                                              mb: 2,
                                            }}
                                          >
                                            <img
                                              src={card.headerMediaUrl}
                                              alt={`Carousel ${index + 1}`}
                                              style={{
                                                width: "100%",
                                                maxWidth: 200,
                                                maxHeight: 200,
                                                height: "auto",
                                                objectFit: "contain",
                                                borderRadius: 8,
                                                border: "1px solid #d1d5db",
                                                background: "#f8fafc",
                                              }}
                                            />
                                          </Box>
                                        )}
                                        {/* Card Body with formatted text */}
                                        <Typography
                                          variant="body1"
                                          sx={{ mb: 1 }}
                                          dangerouslySetInnerHTML={{
                                            __html: formatBodyText(
                                              unescapeJsonString(card.body)
                                            ),
                                          }}
                                        />

                                        {/* Carousel Card Buttons */}
                                        {Array.isArray(card.carouselButtons) &&
                                          card.carouselButtons.length > 0 && (
                                            <Box
                                              sx={{
                                                mt: 2,
                                                display: "flex",
                                                gap: 1,
                                                flexWrap: "wrap",
                                                justifyContent: "center",
                                              }}
                                            >
                                              {card.carouselButtons.map(
                                                (btn: any, bIdx: number) => (
                                                  <Box
                                                    key={bIdx}
                                                    sx={{
                                                      px: 2,
                                                      py: 0.5,
                                                      borderRadius: 2,
                                                      border: "1px solid",
                                                      borderColor: "#00934F",
                                                      color: "#00934F",
                                                      fontWeight: 500,
                                                      fontSize: 13,
                                                      background: "white",
                                                      cursor: "pointer",
                                                      display: "inline-block",
                                                    }}
                                                  >
                                                    {btn.buttonName ||
                                                      btn.buttonValue}
                                                  </Box>
                                                )
                                              )}
                                            </Box>
                                          )}
                                        {/* Quick Reply Buttons */}
                                        {Array.isArray(card.quickReply) &&
                                          card.quickReply.length > 0 && (
                                            <Box
                                              sx={{
                                                mt: 2,
                                                display: "flex",
                                                gap: 1,
                                                flexWrap: "wrap",
                                                justifyContent: "center",
                                              }}
                                            >
                                              {card.quickReply.map(
                                                (btn: string, bIdx: number) => (
                                                  <Box
                                                    key={bIdx}
                                                    sx={{
                                                      px: 2,
                                                      py: 0.5,
                                                      borderRadius: 2,
                                                      border: "1px solid",
                                                      borderColor: "#00934F",
                                                      color: "#00934F",
                                                      fontWeight: 500,
                                                      fontSize: 13,
                                                      background: "white",
                                                      cursor: "pointer",
                                                      display: "inline-block",
                                                    }}
                                                  >
                                                    {btn}
                                                  </Box>
                                                )
                                              )}
                                            </Box>
                                          )}
                                        {/* URL Buttons */}
                                        {Array.isArray(card.urlButtonName) &&
                                          card.urlButtonName.length > 0 && (
                                            <Box
                                              sx={{
                                                mt: 2,
                                                display: "flex",
                                                gap: 1,
                                                flexWrap: "wrap",
                                                justifyContent: "center",
                                              }}
                                            >
                                              {card.urlButtonName.map(
                                                (btn: string, bIdx: number) => (
                                                  <Box
                                                    key={bIdx}
                                                    sx={{
                                                      px: 2,
                                                      py: 0.5,
                                                      borderRadius: 2,
                                                      border: "1px solid",
                                                      borderColor: "#00934F",
                                                      color: "#00934F",
                                                      fontWeight: 500,
                                                      fontSize: 13,
                                                      background: "white",
                                                      cursor: "pointer",
                                                      display: "inline-block",
                                                    }}
                                                  >
                                                    {btn}
                                                  </Box>
                                                )
                                              )}
                                            </Box>
                                          )}
                                        {/* Phone Number Button */}
                                        {card.callButtonName && (
                                          <Box
                                            sx={{
                                              mt: 2,
                                              display: "flex",
                                              gap: 1,
                                              flexWrap: "wrap",
                                              justifyContent: "center",
                                            }}
                                          >
                                            <Box
                                              sx={{
                                                px: 2,
                                                py: 0.5,
                                                borderRadius: 2,
                                                border: "1px solid",
                                                borderColor: "#00934F",
                                                color: "#00934F",
                                                fontWeight: 500,
                                                fontSize: 13,
                                                background: "white",
                                                cursor: "pointer",
                                                display: "inline-block",
                                              }}
                                            >
                                              {card.callButtonName}
                                            </Box>
                                          </Box>
                                        )}
                                      </CardContent>
                                    </Card>
                                  </Box>
                                )
                              )}
                            </Box>
                            {/* Dots/Indicators */}
                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "center",
                                mt: 1,
                              }}
                            >
                              {templateState.carouselCards.map((_, idx) => (
                                <Box
                                  key={idx}
                                  onClick={() => setCarouselIndex(idx)}
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: "50%",
                                    background:
                                      idx === carouselIndex
                                        ? "#00934F"
                                        : "#e0e0e0",
                                    mx: 0.5,
                                    cursor: "pointer",
                                  }}
                                />
                              ))}
                            </Box>
                          </Box>
                        </Box>
                      )}
                  </CardContent>
                </Card>
              ) : (
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    minHeight: 300,
                    width: "100%",
                    zIndex: 1,
                    pointerEvents: "none",
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: "text.secondary",
                      fontSize: 20,
                      textAlign: "center",
                    }}
                  >
                    Please select a template...
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Right Panel */}
            <Box
              ref={rightPanelRef}
              onScroll={handleRightScroll}
              sx={{
                flex: 1,
                overflowY: isSmallScreen ? "visible" : "auto",
                height: isSmallScreen ? "auto" : "100%",
                pl: isSmallScreen ? 0 : 2,
                pt: isSmallScreen ? 2 : 0,
                borderLeft:
                  !isSmallScreen && templateState?.templateId
                    ? "1px solid #eee"
                    : "none",
                position: "relative",
                width: isSmallScreen ? "100%" : undefined,
                minWidth: 0,
                ...(isSmallScreen
                  ? {
                      scrollbarWidth: "none",
                      "&::-webkit-scrollbar": { display: "none" },
                    }
                  : {
                      scrollbarWidth: "none",
                      "&::-webkit-scrollbar": { display: "none" },
                      msOverflowStyle: "none",
                    }),
              }}
            >
              <Box sx={{ position: "relative", zIndex: 2, height: "100%" }}>
                <>
                  {templateState?.variables?.length > 0 ||
                  leadratVariables.length > 0 ||
                  carouselBodyVariables.length > 0 ||
                  carouselLeadratBodyVariables.length > 0 ? (
                    <Card sx={{ p: 0, mb: 3, boxShadow: 2 }}>
                      <CardHeader
                        title={
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 600 }}
                          >
                            Variables
                          </Typography>
                        }
                        sx={{ p: 0, pl: 2, pt: 2 }}
                      />
                      <CardContent>
                        {headerVariables.length > 0 && (
                          <>
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, mb: 3 }}
                            >
                              Header Variables
                            </Typography>
                            <Grid
                              container
                              spacing={2}
                              sx={{ background: "#f8fafc" }}
                            >
                              <Grid item xs={12}>
                                <Box sx={{ display: "flex", gap: 2, mb: 1 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Variable
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Value
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Fallback Value
                                  </Typography>
                                </Box>
                              </Grid>
                              {headerVariables.map((variable, index) => (
                                <Grid item xs={12} key={index}>
                                  <Box sx={{ display: "flex", gap: 2 }}>
                                    <TextFieldWithBorderComponent
                                      name="variable"
                                      size="small"
                                      variant="outlined"
                                      value={variable?.id}
                                      disabled
                                      label="Variable"
                                      placeholder=""
                                      sx={{ width: "200px" }}
                                    />
                                    <FormControl
                                      size="small"
                                      error={!!variableErrors[index]?.value}
                                      sx={{ mt: 1, width: "200px" }}
                                    >
                                      <Select
                                        value={variable?.field || ""}
                                        onChange={(event: any) =>
                                          handleVariableValueChange(
                                            event,
                                            "value",
                                            index,
                                            "header"
                                          )
                                        }
                                        name="value"
                                        displayEmpty
                                        inputProps={{ style: { fontSize: 14 } }}
                                        sx={{
                                          fontSize: 14,
                                          borderRadius: "8px",
                                        }}
                                      >
                                        <MenuItem value="" disabled>
                                          Select Value
                                        </MenuItem>
                                        {contactColumnNames?.map((item) => (
                                          <MenuItem
                                            value={item?.name}
                                            key={item?.name}
                                            sx={{
                                              fontSize: { xs: 10, md: 12 },
                                            }}
                                          >
                                            {item?.name}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                      {variableErrors[index]?.value && (
                                        <FormHelperText>
                                          {variableErrors[index].value}
                                        </FormHelperText>
                                      )}
                                    </FormControl>
                                    <TextFieldWithBorderComponent
                                      name="fallBackValue"
                                      size="small"
                                      variant="outlined"
                                      value={variable?.fallBackValue}
                                      onChange={(event: any) =>
                                        handleVariableValueChange(
                                          event,
                                          "fallBackValue",
                                          index,
                                          "header"
                                        )
                                      }
                                      error={
                                        !!variableErrors[index]?.fallbackValue
                                      }
                                      helperText={
                                        variableErrors[index]?.fallbackValue
                                      }
                                      label="Fallback Value"
                                      placeholder=""
                                      sx={{ width: "200px" }}
                                    />
                                  </Box>
                                </Grid>
                              ))}
                            </Grid>
                          </>
                        )}
                        {bodyVariables.length > 0 && (
                          <>
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, mb: 3, mt: 1 }}
                            >
                              Body Variables
                            </Typography>
                            <Grid
                              container
                              spacing={2}
                              sx={{ background: "#f8fafc" }}
                            >
                              <Grid item xs={12}>
                                <Box sx={{ display: "flex", gap: 2, mb: 1 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Variable
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Value
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ width: "200px", fontWeight: 500 }}
                                  >
                                    Fallback Value
                                  </Typography>
                                </Box>
                              </Grid>
                              {bodyVariables.map((variable, index) => (
                                <Grid item xs={12} key={index}>
                                  <Box sx={{ display: "flex", gap: 2 }}>
                                    <TextFieldWithBorderComponent
                                      name="variable"
                                      size="small"
                                      variant="outlined"
                                      value={variable?.id}
                                      disabled
                                      label="Variable"
                                      placeholder=""
                                      sx={{ width: "200px" }}
                                    />
                                    <FormControl
                                      size="small"
                                      error={!!variableErrors[index]?.value}
                                      sx={{ mt: 1, width: "200px" }}
                                    >
                                      <Select
                                        value={variable?.field || ""}
                                        onChange={(event: any) =>
                                          handleVariableValueChange(
                                            event,
                                            "value",
                                            index,
                                            "body"
                                          )
                                        }
                                        name="value"
                                        displayEmpty
                                        inputProps={{ style: { fontSize: 14 } }}
                                        sx={{
                                          fontSize: 14,
                                          // borderRadius: "8px",
                                        }}
                                      >
                                        <MenuItem value="" disabled>
                                          Select Value
                                        </MenuItem>
                                        {contactColumnNames?.map((item) => (
                                          <MenuItem
                                            value={item?.name}
                                            key={item?.name}
                                            sx={{
                                              fontSize: { xs: 10, md: 12 },
                                            }}
                                          >
                                            {item?.name}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                      {variableErrors[index]?.value && (
                                        <FormHelperText>
                                          {variableErrors[index].value}
                                        </FormHelperText>
                                      )}
                                    </FormControl>
                                    <TextFieldWithBorderComponent
                                      name="fallBackValue"
                                      size="small"
                                      variant="outlined"
                                      value={variable?.fallBackValue}
                                      onChange={(event: any) =>
                                        handleVariableValueChange(
                                          event,
                                          "fallBackValue",
                                          index,
                                          "body"
                                        )
                                      }
                                      error={
                                        !!variableErrors[index]?.fallbackValue
                                      }
                                      helperText={
                                        variableErrors[index]?.fallbackValue
                                      }
                                      label="Fallback Value"
                                      placeholder=""
                                      sx={{ width: "200px" }}
                                    />
                                  </Box>
                                </Grid>
                              ))}
                            </Grid>
                          </>
                        )}
                        {leadratVariables.length > 0 && (
                          <>
                            {/* Header Variables */}
                            {leadratVariables.filter((v) => v.type === "header")
                              .length > 0 && (
                              <Box
                                sx={{
                                  background: "#f8fafc",
                                  mb: 2,
                                  border: "1px solid #e0e0e0",
                                  borderRadius: "4px",
                                  p: 2,
                                }}
                              >
                                <Typography
                                  variant="subtitle2"
                                  sx={{
                                    fontWeight: 500,
                                    mb: 2,
                                    color: "text.secondary",
                                  }}
                                >
                                  Header Variables
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12}>
                                    <Box
                                      sx={{ display: "flex", gap: 2, mb: 1 }}
                                    >
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Variable
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Value
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Fallback Value
                                      </Typography>
                                    </Box>
                                  </Grid>
                                  {leadratVariables
                                    .filter((v) => v.type === "header")
                                    .map((variable: any, index: number) => (
                                      <Grid item xs={12} key={index}>
                                        <Box sx={{ display: "flex", gap: 2 }}>
                                          <TextFieldWithBorderComponent
                                            name="variable"
                                            size="small"
                                            variant="outlined"
                                            value={variable?.id}
                                            disabled
                                            label="Variable"
                                            placeholder=""
                                            sx={{ width: "200px" }}
                                          />
                                          <FormControl
                                            size="small"
                                            error={
                                              !!variableErrors[`leadrat_header_${index}`]?.value
                                            }
                                            sx={{ mt: 1, width: "200px" }}
                                          >
                                            <Select
                                              value={variable?.field || ""}
                                              onChange={(event: any) =>
                                                handleLeadratVariableValueChange(
                                                  event,
                                                  "value",
                                                  index,
                                                  "header"
                                                )
                                              }
                                              name="value"
                                              displayEmpty
                                              inputProps={{
                                                style: { fontSize: 14 },
                                              }}
                                              sx={{ fontSize: 14 }}
                                            >
                                              <MenuItem value="" disabled>
                                                Select Value
                                              </MenuItem>
                                              {contactColumnNames.map(
                                                (column) => (
                                                  <MenuItem
                                                    key={column.name}
                                                    value={column.name}
                                                  >
                                                    {column.name}
                                                  </MenuItem>
                                                )
                                              )}
                                            </Select>
                                            {variableErrors[`leadrat_header_${index}`]?.value && (
                                              <FormHelperText>
                                                {variableErrors[`leadrat_header_${index}`].value}
                                              </FormHelperText>
                                            )}
                                          </FormControl>
                                          <TextFieldWithBorderComponent
                                            name="fallBackValue"
                                            size="small"
                                            variant="outlined"
                                            value={variable?.fallBackValue}
                                            onChange={(event: any) =>
                                              handleLeadratVariableValueChange(
                                                event,
                                                "fallBackValue",
                                                index,
                                                "header"
                                              )
                                            }
                                            error={
                                              !!variableErrors[`leadrat_header_${index}`]?.fallbackValue
                                            }
                                            helperText={
                                              variableErrors[`leadrat_header_${index}`]?.fallbackValue
                                            }
                                            label="Fallback Value"
                                            placeholder=""
                                            sx={{ width: "200px" }}
                                          />
                                        </Box>
                                      </Grid>
                                    ))}
                                </Grid>
                              </Box>
                            )}

                            {/* Body Variables */}
                            {leadratVariables.filter((v) => v.type === "body")
                              .length > 0 && (
                              <Box
                                sx={{
                                  background: "#f8fafc",
                                  border: "1px solid #e0e0e0",
                                  borderRadius: "4px",
                                  p: 2,
                                }}
                              >
                                <Typography
                                  variant="subtitle2"
                                  sx={{
                                    fontWeight: 500,
                                    mb: 2,
                                    color: "text.secondary",
                                  }}
                                >
                                  Body Variables
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12}>
                                    <Box
                                      sx={{ display: "flex", gap: 2, mb: 1 }}
                                    >
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Variable
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Value
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ width: "200px", fontWeight: 500 }}
                                      >
                                        Fallback Value
                                      </Typography>
                                    </Box>
                                  </Grid>
                                  {leadratVariables
                                    .filter((v) => v.type === "body")
                                    .map((variable: any, index: number) => (
                                      <Grid item xs={12} key={index}>
                                        <Box sx={{ display: "flex", gap: 2 }}>
                                          <TextFieldWithBorderComponent
                                            name="variable"
                                            size="small"
                                            variant="outlined"
                                            value={variable?.id}
                                            disabled
                                            label="Variable"
                                            placeholder=""
                                            sx={{ width: "200px" }}
                                          />
                                          <FormControl
                                            size="small"
                                            error={
                                              !!variableErrors[`leadrat_body_${index}`]?.value
                                            }
                                            sx={{ mt: 1, width: "200px" }}
                                          >
                                            <Select
                                              value={variable?.field || ""}
                                              onChange={(event: any) =>
                                                handleLeadratVariableValueChange(
                                                  event,
                                                  "value",
                                                  index,
                                                  "body"
                                                )
                                              }
                                              name="value"
                                              displayEmpty
                                              inputProps={{
                                                style: { fontSize: 14 },
                                              }}
                                              sx={{ fontSize: 14 }}
                                            >
                                              <MenuItem value="" disabled>
                                                Select Value
                                              </MenuItem>
                                              {contactColumnNames.map(
                                                (column) => (
                                                  <MenuItem
                                                    key={column.name}
                                                    value={column.name}
                                                  >
                                                    {column.name}
                                                  </MenuItem>
                                                )
                                              )}
                                            </Select>
                                            {variableErrors[`leadrat_body_${index}`]?.value && (
                                              <FormHelperText>
                                                {variableErrors[`leadrat_body_${index}`].value}
                                              </FormHelperText>
                                            )}
                                          </FormControl>
                                          <TextFieldWithBorderComponent
                                            name="fallBackValue"
                                            size="small"
                                            variant="outlined"
                                            value={variable?.fallBackValue}
                                            onChange={(event: any) =>
                                              handleLeadratVariableValueChange(
                                                event,
                                                "fallBackValue",
                                                index,
                                                "body"
                                              )
                                            }
                                            error={
                                              !!variableErrors[`leadrat_body_${index}`]?.fallbackValue
                                            }
                                            helperText={
                                              variableErrors[`leadrat_body_${index}`]?.fallbackValue
                                            }
                                            label="Fallback Value"
                                            placeholder=""
                                            sx={{ width: "200px" }}
                                          />
                                        </Box>
                                      </Grid>
                                    ))}
                                </Grid>
                              </Box>
                            )}
                          </>
                        )}
                        {carouselBodyVariables.length > 0 && (
                          <>
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, mb: 1, my: 3 }}
                            >
                              Carousel Variables
                            </Typography>
                            {carouselBodyVariables.map(
                              (
                                currentCarousel: any,
                                currentCarouselIndex: number
                              ) => (
                                <Card
                                  key={currentCarouselIndex}
                                  sx={{
                                    mb: 2,
                                    background: "#f8fafc",
                                    boxShadow: 0,
                                    border: "1px solid #e0e0e0",
                                  }}
                                >
                                  <CardHeader
                                    title={
                                      <Typography variant="subtitle2">
                                        Carousel {currentCarouselIndex + 1}
                                      </Typography>
                                    }
                                    sx={{ pb: 0 }}
                                  />
                                  <CardContent>
                                    <Grid container spacing={2}>
                                      <Grid item xs={12}>
                                        <Box
                                          sx={{
                                            display: "flex",
                                            gap: 2,
                                            mb: 1,
                                          }}
                                        >
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Variable
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Value
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Fallback Value
                                          </Typography>
                                        </Box>
                                      </Grid>
                                      {currentCarousel?.map(
                                        (variable: any, index: number) => (
                                          <Grid item xs={12} key={index}>
                                            <Box
                                              sx={{ display: "flex", gap: 2 }}
                                            >
                                              <TextFieldWithBorderComponent
                                                name="variable"
                                                size="small"
                                                variant="outlined"
                                                value={variable?.id}
                                                disabled
                                                label="Variable"
                                                placeholder=""
                                                sx={{ width: "200px" }}
                                              />
                                              <FormControl
                                                size="small"
                                                error={
                                                  !!variableErrors[index]?.value
                                                }
                                                sx={{ mt: 1, width: "200px" }}
                                              >
                                                <Select
                                                  value={variable?.field || ""}
                                                  onChange={(event: any) =>
                                                    handleCarouselBodyVariableValueChange(
                                                      event,
                                                      "value",
                                                      index,
                                                      currentCarouselIndex
                                                    )
                                                  }
                                                  name="value"
                                                  displayEmpty
                                                  inputProps={{
                                                    style: { fontSize: 14 },
                                                  }}
                                                  sx={{
                                                    fontSize: 14,
                                                    // borderRadius: "8px",
                                                  }}
                                                >
                                                  <MenuItem value="" disabled>
                                                    Select Value
                                                  </MenuItem>
                                                  {contactColumnNames?.map(
                                                    (item) => (
                                                      <MenuItem
                                                        value={item?.name}
                                                        key={item?.name}
                                                        sx={{
                                                          fontSize: {
                                                            xs: 10,
                                                            md: 12,
                                                          },
                                                        }}
                                                      >
                                                        {item?.name}
                                                      </MenuItem>
                                                    )
                                                  )}
                                                </Select>
                                                {variableErrors[index]
                                                  ?.value && (
                                                  <FormHelperText>
                                                    {
                                                      variableErrors[index]
                                                        .value
                                                    }
                                                  </FormHelperText>
                                                )}
                                              </FormControl>
                                              <TextFieldWithBorderComponent
                                                name="fallBackValue"
                                                size="small"
                                                variant="outlined"
                                                value={variable?.fallBackValue}
                                                onChange={(event: any) =>
                                                  handleCarouselBodyVariableValueChange(
                                                    event,
                                                    "fallBackValue",
                                                    index,
                                                    currentCarouselIndex
                                                  )
                                                }
                                                error={
                                                  !!variableErrors[index]
                                                    ?.fallbackValue
                                                }
                                                helperText={
                                                  variableErrors[index]
                                                    ?.fallbackValue
                                                }
                                                label="Fallback Value"
                                                placeholder=""
                                                sx={{ width: "200px" }}
                                              />
                                            </Box>
                                          </Grid>
                                        )
                                      )}
                                    </Grid>
                                  </CardContent>
                                </Card>
                              )
                            )}
                          </>
                        )}
                        {carouselLeadratBodyVariables.length > 0 && (
                          <>
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, mb: 1, my: 3 }}
                            >
                              Carousel Leadrat Variables
                            </Typography>
                            {carouselLeadratBodyVariables.map(
                              (
                                currentCarousel: any,
                                currentCarouselIndex: number
                              ) => (
                                <Card
                                  key={currentCarouselIndex}
                                  sx={{
                                    mb: 2,
                                    background: "#f8fafc",
                                    boxShadow: 0,
                                    border: "1px solid #e0e0e0",
                                  }}
                                >
                                  <CardHeader
                                    title={
                                      <Typography variant="subtitle2">
                                        Carousel {currentCarouselIndex + 1}
                                      </Typography>
                                    }
                                    sx={{ pb: 0 }}
                                  />
                                  <CardContent>
                                    <Grid container spacing={2}>
                                      <Grid item xs={12}>
                                        <Box
                                          sx={{
                                            display: "flex",
                                            gap: 2,
                                            mb: 1,
                                          }}
                                        >
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Variable
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Value
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              width: "200px",
                                              fontWeight: 500,
                                            }}
                                          >
                                            Fallback Value
                                          </Typography>
                                        </Box>
                                      </Grid>
                                      {currentCarousel?.map(
                                        (variable: any, index: number) => (
                                          <Grid item xs={12} key={index}>
                                            <Box
                                              sx={{ display: "flex", gap: 2 }}
                                            >
                                              <TextFieldWithBorderComponent
                                                name="variable"
                                                size="small"
                                                variant="outlined"
                                                value={variable?.id}
                                                disabled
                                                label="Variable"
                                                placeholder=""
                                                sx={{ width: "200px" }}
                                              />
                                              <FormControl
                                                size="small"
                                                error={
                                                  !!variableErrors[index]?.value
                                                }
                                                sx={{ mt: 1, width: "200px" }}
                                              >
                                                <Select
                                                  value={variable?.field || ""}
                                                  onChange={(event: any) =>
                                                    handleCarouselLeadratBodyVariableValueChange(
                                                      event,
                                                      "value",
                                                      index,
                                                      currentCarouselIndex
                                                    )
                                                  }
                                                  name="value"
                                                  displayEmpty
                                                  inputProps={{
                                                    style: { fontSize: 14 },
                                                  }}
                                                  sx={{
                                                    fontSize: 14,
                                                    borderRadius: "8px",
                                                  }}
                                                >
                                                  <MenuItem value="" disabled>
                                                    Select Value
                                                  </MenuItem>
                                                  {contactColumnNames?.map(
                                                    (item) => (
                                                      <MenuItem
                                                        value={item?.name}
                                                        key={item?.name}
                                                        sx={{
                                                          fontSize: {
                                                            xs: 10,
                                                            md: 12,
                                                          },
                                                        }}
                                                      >
                                                        {item?.name}
                                                      </MenuItem>
                                                    )
                                                  )}
                                                </Select>
                                                {variableErrors[index]
                                                  ?.value && (
                                                  <FormHelperText>
                                                    {
                                                      variableErrors[index]
                                                        .value
                                                    }
                                                  </FormHelperText>
                                                )}
                                              </FormControl>
                                              <TextFieldWithBorderComponent
                                                name="fallBackValue"
                                                size="small"
                                                variant="outlined"
                                                value={variable?.fallBackValue}
                                                onChange={(event: any) =>
                                                  handleCarouselLeadratBodyVariableValueChange(
                                                    event,
                                                    "fallBackValue",
                                                    index,
                                                    currentCarouselIndex
                                                  )
                                                }
                                                error={
                                                  !!variableErrors[index]
                                                    ?.fallbackValue
                                                }
                                                helperText={
                                                  variableErrors[index]
                                                    ?.fallbackValue
                                                }
                                                label="Fallback Value"
                                                placeholder=""
                                                sx={{ width: "200px" }}
                                              />
                                            </Box>
                                          </Grid>
                                        )
                                      )}
                                    </Grid>
                                  </CardContent>
                                </Card>
                              )
                            )}
                          </>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    templateState?.templateId && (
                      <Box
                        sx={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          minHeight: 300,
                          width: "100%",
                          zIndex: 1,
                          pointerEvents: "none",
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 600,
                            color: "text.secondary",
                            fontSize: 20,
                            textAlign: "center",
                          }}
                        >
                          -- No Variables --
                        </Typography>
                      </Box>
                    )
                  )}
                </>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        {/* <TemplateComponent
          editObjectData={editObjectData}
          open={openCreateTemplateDialog}
          handleClose={handleCloseCreateTemplateDialog}
        /> */}
        <DialogActions sx={{ mx: 3, my: 1 }}>
          <Box
            mr={3}
            className={classes.insertButton}
            onClick={() => {
              setVariableErrors({});
              setCarouselIndex(0);
              setTemplateState({
                templateId: "",
                templateName: "",
                category: 1,
                subCategory: "",
                mediaType: 1,
                mediaAwsUrl: null,
                header: "",
                body: "",
                callButtonName: "",
                phoneNumber: "",
                countryCode: "",
                urlButtonName: [],
                redirectUrl: [],
                quickReply: [],
                codeDeliverySetup: "COPY_CODE",
                zeroTapAuthentication: false,
                appSetup: [{ appPackageName: "", appSignatureHash: "" }],
                addSecurityRecommendation: true,
                addExpiryTime: false,
                codeExpiresIn: 10,

                autoFillButtonText: "Auto fill",
                copyCodeButtonText: "Copy code",
                messageValidity: true,
                messageValidityPeriod: 300,
                footer: "",
                buttons: [],
                variables: [],
                leadratVariables: [],
                carouselCards: [],
              });
            }}
          >
            <button
              className={classes.buttonText}
              // style={{ color: bgColors.red }}
            >
              Reset
            </button>
          </Box>
          <Box
            mr={3}
            onClick={() => handlePreview()}
            className={classes.insertButton}
          >
            <button
              className={classes.buttonText}
              // style={{ color: bgColors.blue }}
            >
              Preview
            </button>
          </Box>
          <Tooltip
            title="Access Denied"
            open={templateTooltip}
            placement="top"
            onClose={() => setTemplateTooltip(false)}
          >
            <Box
              onClick={() => handleInsertTemplate()}
              className={classes.insertButton}
              mr={4}
            >
              <button
                className={classes.buttonText}
                // style={{ color: bgColors.green }}
              >
                {contactNumber ? "Send" : "Insert"}
              </button>
            </Box>
          </Tooltip>
        </DialogActions>
      </Dialog>
      <PreviewDialog />
    </>
  );
};

export default TemplatePopUp;
