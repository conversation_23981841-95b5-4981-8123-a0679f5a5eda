import { Box, Chip, IconButton, Paper, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { NodeProps, Position, useReactFlow } from "reactflow";
import { bgColors } from "../../../utils/bgColors";
import CustomHandle from "./CustomHandle";
import MessageEditorPanelFlowStart from "./MessageEditorPanelFlowStart";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";
import { toastActions } from "../../../utils/toastSlice";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { getWorkflowAllKeywords } from "../../../redux/slices/Workflows/getWorkflowAllKeywordsSlice";
import { useParams } from "react-router-dom";
import { getKeywords } from "../../../redux/slices/Workflows/getKeywordsSlice";
import { GetAllStatusByTenantId } from "../../../redux/slices/Utility/GetAllStatusByTenantId";
import { GetAllProjectsByTenantId } from "../../../redux/slices/Utility/GetAllProjectsByTenantId";
import { getFlowstartNodes } from "../../../redux/slices/Workflows/getFlowstartNodesSlice";

// Define the interface for the source object in newLead
interface SourceObject {
  source: string;
  subSources: string[];
}

interface StatusChangeObject {
  status: string;
  subStatus: string[];
}

const FlowStartNode = ({ data, id }: NodeProps) => {
  const {
    keywords,
    leadSource,
    leadProject,
    triggerType,
    isMessagePanelOpen,
    statusChange,
  } = data;
  const { setNodes, getNode } = useReactFlow();
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);

  const tenantId = useAppSelector(
    (state: any) => state.companyData?.data?.company?.tenantId
  );

  const [sourcesData, setSourcesData] = useState<any[]>([]);
  const [statusWithSubStatusData, setStatusWithSubStatusData] = useState<any[]>(
    []
  );
  const [projectData, setProjectData] = useState<any[]>([]);
  const [newKeyword, setNewKeyword] = useState("");
  const keywordsSlice = useAppSelector((state: any) => state?.getKeywords);
  const keywordsData = keywordsSlice?.getKeywordsData;
  const allKeywords = keywordsData?.data;
  const flowStartNodesData = useAppSelector((state: any) =>
    state?.getFlowstartNodes?.getFlowstartNodesData?.data?.map(
      (item: any) => item
    )
  );

  const allLeadSources =
    flowStartNodesData
      ?.filter((item: any) => item.id !== id)
      ?.map((item: any) => item?.data?.flowStart?.leadSource)
      .filter((item: any) => item !== null)
      .flat() || [];
  const allLeadStatusChanges =
    flowStartNodesData
      ?.filter((item: any) => item.id !== id)
      ?.map((item: any) => item?.data?.flowStart?.leadStatus)
      .filter((item: any) => item !== null)
      .flat() || [];
  const allLeadProjects =
    flowStartNodesData
      ?.filter((item: any) => item.id !== id)
      ?.map((item: any) => item?.data?.flowStart?.leadProject)
      .filter((item: any) => item !== null)
      .flat()
      .filter(
        (project: string, index: number, self: string[]) =>
          self.indexOf(project) === index
      ) || [];

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds: any[]) =>
      nds.map((node: any) => {
        if (node.id === id) {
          const newData = updateFn(node.data);

          // FlowStartNode doesn't have validation requirements according to the validation logic
          // So we can always set isValid to true or just not set it at all
          // Since FlowStartNode is exempt from validation

          // We don't need to check if isValid is false first since FlowStartNode is always valid
          newData.isValid = true;

          return { ...node, data: newData };
        }
        return node;
      })
    );
  };

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };

  const handleTriggerTypeChange = async (value: string) => {
    updateNodeData((data) => {
      const newData = {
        ...data,
        triggerType: value,
        ...(value === "keywords" ? { leadSource: [] } : { keywords: [] }),
        ...(value === "statusChange" ? { statusChange: [] } : []),
        ...(value === "leadProject" ? { leadProject: [] } : []),
      };
      return newData;
    });
    if (value !== "keywords") {
      setNewKeyword("");
    }
  };

  // keywords change: update the keywords in the node data
  const handleKeywordsChange = (value: string[]) => {
    updateNodeData((data) => ({
      ...data,
      keywords: value,
      isMessagePanelOpen: data.isMessagePanelOpen,
    }));
  };

  const arrayToObjectLead = (
    leadArray: SourceObject[]
  ): Record<string, string[]> => {
    const obj: Record<string, string[]> = {};
    leadArray?.forEach((item) => {
      obj[item.source] = item.subSources || [];
    });
    return obj;
  };

  const handleSubSourceChange = (selected: Record<string, string[]>) => {
    // selected: { [source]: [subSource, ...] }
    const newLeadArray: SourceObject[] = Object.entries(selected).map(
      ([source, subSources]) => ({
        source,
        subSources,
      })
    );

    updateNodeData((data) => ({
      ...data,
      leadSource: newLeadArray,
    }));
  };

  async function getStatusWithSubStatusData() {
    try {
      const data = {
        page: 1,
        pageSize: 10,
        tenantId: tenantId,
      };
      const res = await dispatch(GetAllStatusByTenantId(data));
      if (
        res?.meta?.requestStatus === "fulfilled" &&
        res?.payload?.succeeded === true
      ) {
        const statusData = res?.payload?.items;

        const statusWithSubStatus = statusData?.map((item: any) => ({
          status: item.displayName,
          subStatus:
            item.childTypes?.map((subStatus: any) => subStatus.displayName) ||
            [],
        }));
        setStatusWithSubStatusData(statusWithSubStatus);
      }
    } catch {
      dispatch(
        toastActions.setToaster({
          message:
            "An error occurred while fetching the status with sub-status data",
          type: "error",
        })
      );
    }
  }

  const arrayToObjectStatus = (
    statusArray: StatusChangeObject[]
  ): Record<string, string[]> => {
    const obj: Record<string, string[]> = {};
    statusArray?.forEach((item) => {
      obj[item.status] = item.subStatus || [];
    });
    return obj;
  };

  const handleSubStatusChange = (selected: Record<string, string[]>) => {
    const newStatusWithSubStatus: StatusChangeObject[] = Object.entries(
      selected
    ).map(([status, subStatus]) => ({
      status,
      subStatus,
    }));
    updateNodeData((data) => ({
      ...data,
      statusChange: newStatusWithSubStatus,
    }));
  };

  async function getSourcesAndSubSources() {
    try {
      const res = await WORKFLOW_API.getSourcesAndSubSources({
        businessId: userData?.companyId,
      });
      setSourcesData(
        res?.data?.data?.filter(
          (source: any) => source.source.toLowerCase() !== "none"
        ) || []
      );
    } catch {
      dispatch(
        toastActions.setToaster({
          message:
            "An error occurred while fetching the sources and sub-sources",
          type: "error",
        })
      );
    }
  }

  async function getProjectNames() {
    const data = {
      page: 1,
      pageSize: 10,
      tenantId: tenantId,
    };
    try {
      const res = await dispatch(GetAllProjectsByTenantId(data));
      if (
        res?.meta?.requestStatus === "fulfilled" &&
        res?.payload?.succeeded === true
      ) {
        const projectNames = res.payload?.data;
        setProjectData(projectNames);
      }
    } catch {
      dispatch(
        toastActions.setToaster({
          message: "An error occurred while fetching the projects",
          type: "error",
        })
      );
    }
  }

  useEffect(() => {
    getSourcesAndSubSources();
    getStatusWithSubStatusData();
    getProjectNames();
  }, []);

  useEffect(() => {
    dispatch(getKeywords({ companyId: userData?.companyId }));
  }, []);

  useEffect(() => {
    dispatch(getFlowstartNodes({ businessId: userData?.companyId }));
  }, []);

  useEffect(() => {
    updateNodeData((data) => {
      if (data.triggerType === "keywords") {
        return { ...data, leadSource: [], statusChange: [], leadProject: [] };
      }
      if (data.triggerType === "newLead") {
        return { ...data, keywords: [], statusChange: [], leadProject: [] };
      }
      if (data.triggerType === "statusChange") {
        return { ...data, keywords: [], leadSource: [], leadProject: [] };
      }
      if (data.triggerType === "leadProject") {
        return { ...data, keywords: [], leadSource: [], statusChange: [] };
      }
      return data;
    });
  }, [triggerType]);

  return (
    <Paper
      elevation={3}
      sx={{
        // p: 2,
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 280,
        cursor: "auto",
      }}
      className="nowheel"
    >
      {/* Main Node Content - This is the draggable part */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6">Flow Start</Typography>
        <IconButton
          size="small"
          sx={{
            background: "transparent",
            border: "none",
            color: "white",
            cursor: "auto",
            padding: "2px",
            borderRadius: "3px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.2)",
            },
          }}
          onClick={handleEdit}
        >
          <EditOutlinedIcon />
        </IconButton>
      </Box>
      <Box p={2} mt={2}>
        <Typography
          variant="h4"
          sx={{
            margin: "0 0 8px 0",
            fontSize: 14,
            fontWeight: 500,
            color: "#333",
          }}
        >
          {triggerType === "keywords"
            ? "Keywords"
            : triggerType === "newLead"
            ? "Lead Sources"
            : triggerType === "statusChange"
            ? "Lead Status Changes"
            : triggerType === "leadProject"
            ? "Lead Projects"
            : "No Node Selected"}
        </Typography>

        <Box
          mt={1}
          sx={{
            maxHeight: 350,
            overflowY: "auto",
            background: bgColors.green1,
            borderRadius: 2,
            p: 1,
          }}
        >
          {triggerType === "keywords" ? (
            keywords?.length > 0 ? (
              keywords?.map((item: string, index: number) => (
                <Chip
                  key={index}
                  label={item}
                  size="small"
                  sx={{
                    backgroundColor: bgColors.green2,
                    color: "text.secondary",
                    borderRadius: 2,
                    fontSize: 12,
                    mr: 1,
                  }}
                />
              ))
            ) : (
              <Typography variant="body2" color="text.secondary">
                No keywords set
              </Typography>
            )
          ) : triggerType === "newLead" ? (
            leadSource?.length > 0 ? (
              <Box
                sx={{
                  backgroundColor: bgColors.green1,
                  borderRadius: 2,
                  p: 0,
                  boxShadow: 0,
                }}
              >
                {leadSource?.map((sourceObj: any) => {
                  const sourceId = sourceObj?.source;
                  const subSources = sourceObj?.subSources || [];
                  return (
                    <Box
                      key={sourceId}
                      sx={{
                        mb: 3,
                        borderLeft: `4px solid ${bgColors.green}`,
                        pl: 2,
                        py: 1,
                        backgroundColor: bgColors.green1,
                        borderRadius: 2,
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "8px",
                          borderRadius: "4px",
                          backgroundColor: bgColors.green1,
                          border: `1px dashed ${bgColors.green}`,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{
                            color: "text.secondary",
                            fontWeight: 500,
                            mb: 0,
                          }}
                        >
                          {sourceId}
                        </Typography>
                      </Box>
                      {subSources.length > 0 && (
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mt: 1,
                            borderRadius: "4px",
                            overflowY: "auto",
                            maxHeight: "200px",
                          }}
                        >
                          {subSources.map((subSource: string) => (
                            <Chip
                              key={subSource}
                              label={subSource}
                              size="small"
                              sx={{
                                backgroundColor: bgColors.green,
                                color: "#fff",
                                borderRadius: 2,
                                fontSize: 13,
                                fontWeight: 500,
                              }}
                            />
                          ))}
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No lead sources selected
              </Typography>
            )
          ) : triggerType === "statusChange" ? (
            statusChange?.length > 0 ? (
              <Box
                sx={{
                  backgroundColor: bgColors.green1,
                  borderRadius: 2,
                  p: 0,
                  boxShadow: 0,
                }}
              >
                {statusChange.map((statusObj: any) => {
                  const statusId = statusObj.status;
                  const subStatus = statusObj.subStatus || [];
                  return (
                    <Box
                      key={statusObj.status}
                      sx={{
                        mb: 3,
                        borderLeft: `4px solid ${bgColors.green}`,
                        pl: 2,
                        py: 1,
                        backgroundColor: bgColors.green1,
                        borderRadius: 2,
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "8px",
                          borderRadius: "4px",
                          backgroundColor: bgColors.green1,
                          border: `1px dashed ${bgColors.green}`,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{
                            color: "text.secondary",
                            fontWeight: 500,
                            mb: 0,
                          }}
                        >
                          {statusId}
                        </Typography>
                      </Box>
                      {subStatus.length > 0 && (
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mt: 1,
                            borderRadius: "4px",
                            overflowY: "auto",
                            maxHeight: "200px",
                          }}
                        >
                          {subStatus.map((subStatus: string) => (
                            <Chip
                              key={subStatus}
                              label={subStatus}
                              size="small"
                              sx={{
                                backgroundColor: bgColors.green,
                                color: "#fff",
                                borderRadius: 2,
                                fontSize: 13,
                                fontWeight: 500,
                              }}
                            />
                          ))}
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No lead status changes selected
              </Typography>
            )
          ) : triggerType === "leadProject" ? (
            Array.isArray(data.leadProject) && data.leadProject.length > 0 ? (
              <Box sx={{ borderRadius: 2, p: 0, boxShadow: 0 }}>
                {data.leadProject.map((proj: string) => (
                  <Chip
                    key={proj}
                    label={proj}
                    size="small"
                    sx={{
                      backgroundColor: bgColors.green,
                      color: "#fff",
                      borderRadius: 2,
                      fontSize: 13,
                      fontWeight: 500,
                      mr: 1,
                    }}
                  />
                ))}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No project selected
              </Typography>
            )
          ) : (
            <Typography variant="body2" color="text.secondary">
              No Node Selected
            </Typography>
          )}
        </Box>
      </Box>
      <CustomHandle
        type="source"
        position={Position.Right}
        id={`right-nodeId-${id}`}
      />

      {/* Right Sidebar - This is not part of the draggable area */}
      {isMessagePanelOpen && (
        <MessageEditorPanelFlowStart
          keywords={keywords}
          triggerType={triggerType}
          handleEdit={handleEdit}
          handleKeywordsChange={handleKeywordsChange}
          handleTriggerTypeChange={handleTriggerTypeChange}
          sources={sourcesData}
          statusWithSubStatus={statusWithSubStatusData}
          projectData={projectData}
          onSubSourceChange={handleSubSourceChange}
          onSubStatusChange={handleSubStatusChange}
          newLead={arrayToObjectLead(leadSource)}
          newStatusWithSubStatus={arrayToObjectStatus(statusChange)}
          newProject={Array.isArray(leadProject) ? leadProject : []}
          setNewLead={(obj) => {
            const arr: SourceObject[] = Object.entries(obj).map(
              ([source, subSources]) => ({ source, subSources })
            );
            updateNodeData((data) => ({ ...data, leadSource: arr }));
          }}
          setNewStatusWithSubStatus={(obj) => {
            const arr: StatusChangeObject[] = Object.entries(obj).map(
              ([status, subStatus]) => ({ status, subStatus })
            );
            updateNodeData((data) => ({ ...data, statusChange: arr }));
          }}
          setNewProject={(obj: any) => {
            const arr: string = obj;
            updateNodeData((data) => ({ ...data, leadProject: arr }));
          }}
          placeholder={
            triggerType === "keywords"
              ? "Choose keywords..."
              : triggerType === "newLead"
              ? "Choose lead sources..."
              : triggerType === "statusChange"
              ? "Choose lead status changes..."
              : triggerType === "leadProject"
              ? "Choose projects..."
              : "Choose lead sources..."
          }
          newKeyword={newKeyword}
          setNewKeyword={setNewKeyword}
          data={data}
          allKeywords={allKeywords}
          allLeadSources={allLeadSources}
          allLeadStatusChanges={allLeadStatusChanges}
          allLeadProjects={allLeadProjects}
        />
      )}
    </Paper>
  );
};

export default FlowStartNode;
