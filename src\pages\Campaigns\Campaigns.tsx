import {
  // Avatar,
  Box,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Grid,
  IconButton,
  Paper,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import { makeStyles } from "@mui/styles";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import ArrowDownSvg from "../../assets/svgs/ArrowDownSvg";
import CampaignFilterPopOvers from "../../components/CampaingnsComponents/CampaignFilterPopovers";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { getCampaign } from "../../redux/slices/Campaign/GetCampaignSlice";
import LoadingComponent from "../../components/common/LoadingComponent";
import EditCampaign from "../../components/ScheduledComponents/EditCampaign";
import SearchIconSvg2 from "../../assets/svgs/SearchIconSvg2";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import EventIcon from "@mui/icons-material/Event";
import ChecklistRtlIcon from "@mui/icons-material/ChecklistRtl";
import { checkOnetimeCampaignsPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { getCampaignCount } from "../../redux/slices/Campaign/GetCampaignCountSlice";
import { toastActions } from "../../utils/toastSlice";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import DownloadIcon from "@mui/icons-material/Download";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import RedCloseIcon from "../../assets/svgs/RedCloseIcon";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { Add } from "@mui/icons-material";
import { debounce } from "lodash";
import ResendCampaignPopup from "../../components/ScheduledComponents/ResendCampaignPopup";
import { Sync as SyncIcon } from "@mui/icons-material";

interface CreateCampaignState {
  businessId: string;
  name: string;
  audiences: any[];
  scheduleDate: string | null;
  template: {
    id: string;
    mediaType: any;
    bodyValues: { value: string; fallbackValue: string }[] | null;
    headerValue: { value: string; fallbackValue: string } | null;
  };
  text: string;
  mediaUrl: string;
}

export const CampaignStatusEnum = {
  Completed: 1,
  Incompleted: 2,
  Scheduled: 3,
};

export const campaignOptions = [
  {
    id: "1",
    option: "All",
  },
  {
    id: "2",
    option: "last7days",
  },
  {
    id: "3",
    option: "last14days",
  },
  {
    id: "4",
    option: "last30days",
  },
];

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,

    height: "100vh",
    width: "100%",
    overFlow: "hidden !important",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
    overflow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  manageTeamContainer: {
    display: "flex",
    marginTop: "10px",
    // alignItems: "center",
    // gap: "30px",
    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    // color: bgColors.white,
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  messageCountContainer: {
    // border: "1px solid #F2F2F2",
    // borderRadius: "6px",
    // padding: "8px",
    // width: "30px",
    // paddingBottom: 6,
  },
  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    // width: "10px",
    // paddingBottom: 0,
    // minWidth: "130px",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "20px",
    // padding:"5px"
  },
  blur: {
    filter: "blur(5px)",
    opacity: 0.5,
    transition: "filter 0.3s ease-in-out, opacity 0.3s ease-in-out",
  },
  clear: {
    filter: "none",
    opacity: 1,
    transition: "filter 0.3s ease-in-out, opacity 0.3s ease-in-out",
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
    alignItems: "center",
    // marginTop: "2px",
    color: "#ffffff",
  },
  table: {
    minWidth: "950px",
    overflow: "auto",
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
});

const options = [
  {
    id: 1,
    option: "all",
  },
  {
    id: 2,
    option: "last7days",
  },
  {
    id: 3,
    option: "last14days",
  },
  {
    id: 4,
    option: "last30days",
  },
];

const options1 = [
  {
    id: 1,
    option: "All",
  },
  {
    id: 2,
    option: "Last 7 days",
  },
  {
    id: 3,
    option: "Last 14 days",
  },
  {
    id: 4,
    option: "Last 30 days",
  },
];

const Campaigns = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const state = useAppSelector((state: any) => state);
  const campaignsPermissionsArray =
    state?.getUserPermissions?.data?.campaigns || [];
  const getCampaignAnalyticsData = state?.getCampaign?.data?.data || [];
  const getCampaignAnalyticsStatus = state?.getCampaign?.status || "";
  const campaignsCount = state?.getCampaignCount?.data?.data || 0;

  const oneTimeCampaignsPermissionsObject = campaignsPermissionsArray?.find(
    (item: any) => Object?.prototype?.hasOwnProperty?.call(item, "oneTime")
  );

  const oneTimeCampaignsPermissionsActions = oneTimeCampaignsPermissionsObject
    ? oneTimeCampaignsPermissionsObject.oneTime
    : [];
  const hasOnetimeCampaignsPermission = checkOnetimeCampaignsPermission(
    campaignsPermissionsArray
  );

  const [addNewCampaignTooltip, setAddNewCampaignTooltip] = useState(false);
  const [anchorEl1, setAnchorEl1] = React.useState(null);
  const [selectedFilter1, setSelectedFilter1] = React.useState({
    id: "2",
    value: "last7days",
  });

  const [searchCampaignQuery, setSearchCampaignQuery] = useState<string>("");

  const [page, setPage] = useState(1);
  const [openDialog, setOpenDialog] = React.useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [pageData, setPageData] = useState(getCampaignAnalyticsData || []);
  const [isCampaignSaved, setIsCampaignSaved] = useState(false);
  const [isCampaignDataLoading, setIsCampaignDataLoading] = useState(false);
  const [analyticsPopoverAnchorEl, setAnalyticsPopoverAnchorEl] =
    useState<HTMLElement | null>(null);
  const [selectedCampaignAnalytics, setSelectedCampaignAnalytics] =
    useState<any>(null);
  const [downloadingReport, setDownloadingReport] = useState<string | null>(
    null
  );

  const [openResendDialog, setOpenResendDialog] = React.useState(false);
  const [isResendLoading, setIsResendLoading] = React.useState(false);
  const [resendCampaignData, setResendCampaignData] = useState<any>(null);
  const [title, setTitle] = useState("");
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const getMc = getuserPermissionData?.campaigns;
  const scheduledObject = getMc?.find((item: any) =>
    Object?.prototype?.hasOwnProperty?.call(item, "scheduled")
  );
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const accountInfo = useAppSelector((state: any) => state?.accountData?.data);
  const userInfo = userInfoSlice?.data;
  const scheduledActions = scheduledObject ? scheduledObject.scheduled : [];

  const [createCampaignState, setCreateCampaignState] =
    useState<CreateCampaignState>({
      businessId: "",
      name: "",
      audiences: [],
      scheduleDate: "",
      template: {
        id: "",
        mediaType: null,
        bodyValues: null,
        headerValue: null,
      },
      text: "",
      mediaUrl: "",
    });

  const [rowsPerPage] = useState(20);

  const hasAccess = (permission: any) => {
    if (oneTimeCampaignsPermissionsActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleDownloadReport = async (row: any) => {
    setDownloadingReport(row.campaignId); // Set loading state for specific row
    try {
      const response = await CAMPAIGN_API.getCampaignReport({
        campaignId: row.campaignId,
      });

      const data = response?.data;

      if (data) {
        const blob = new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `Report_${row.campaignTitle}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Successfully downloaded the Excel file.",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "Unable to download the Excel file.",
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Error downloading the report.",
        })
      );
    } finally {
      setDownloadingReport(null); // Reset loading state
    }
  };
  const hasAcess = (permission: any) => {
    if (scheduledActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleOpenResendDialog = (title: string) => {
    const hasPermission = hasAcess("editCampaign");
    setTitle(title);
    if (hasPermission) {
      setOpenResendDialog(true);
    }
  };
  const handleResendCampaign = async (row: any) => {
    setIsResendLoading(true);

    const hasPermission =
      userInfo?.roleName === "Owner" || userInfo?.roleName === "Admin"
        ? hasAcess("editCampaign")
        : hasAcess("editCampaign") && row?.createdby === accountInfo?.name;
    if (hasPermission) {
      const response = await CAMPAIGN_API.getCampaignById({
        campaignId: row.campaignId,
      });
      try {
        setSelectedCampaignAnalytics(response.data.data);
        handleOpenResendDialog("Resend");
        setResendCampaignData(row);
      } catch (err) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "Failed to fetch campaign data",
          })
        );
      }
    }
    setIsResendLoading(false);
  };
  const handleCloseResendDialog = () => {
    setOpenResendDialog(false);
  };

  const handleClose1 = () => {
    setAnchorEl1(null);
  };
  const handleFilterClick1 = (event: any) => {
    setAnchorEl1(event.currentTarget);
  };

  const handleOptionClick = (option: string) => {
    const value = option.toLowerCase().replace(/\s+/g, "");

    const formattedOption = {
      id: campaignOptions.find((item) => item.option === value)?.id || "1",
      value: value === "all" ? "All" : value,
    };
    if (formattedOption.id === selectedFilter1.id) {
      handleClose1();
      return;
    }
    setPageData([]);
    setPage(1);
    setSelectedFilter1(formattedOption);
    handleClose1();
  };
  const handleOpenDialog = () => {
    const hasPermissionToCreateCampaign = hasAccess("newCampaign");
    if (hasPermissionToCreateCampaign) {
      setOpenDialog(true);
    } else {
      setAddNewCampaignTooltip(true);
      setTimeout(() => {
        setAddNewCampaignTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleCloseAnalyticsPopover = () => {
    setAnalyticsPopoverAnchorEl(null);
    setSelectedCampaignAnalytics(null);
  };

  // Modify the handleViewReport function
  const handleViewReport = async (
    row: any,
    event: React.MouseEvent<HTMLElement>
  ) => {
    // Store the target element before the async operation
    const targetElement = event.target as HTMLElement;
    setIsCampaignDataLoading(true);
    try {
      const response = await CAMPAIGN_API.getCampaignById({
        campaignId: row.campaignId,
      });
      setSelectedCampaignAnalytics(response.data.data);
      // Use the stored target element
      setAnalyticsPopoverAnchorEl(targetElement.closest("button"));
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Failed to fetch campaign data",
        })
      );
    } finally {
      setIsCampaignDataLoading(false);
    }
  };

  useEffect(() => {
    setPageData(getCampaignAnalyticsData);
  }, [getCampaignAnalyticsData]);

  const debounceFetchCampaignCount = useCallback(
    debounce(async (data) => {
      dispatch(getCampaignCount(data));
    }, 500),
    []
  );
  useEffect(() => {
    const data = {
      status: CampaignStatusEnum.Completed,
      filters: {
        Condition: {
          Column: "dateSetLive",
          Operator: selectedFilter1.value.toLowerCase(),
          Value: new Date().toISOString().slice(0, 10),
        },
        Search: {
          Column: "campaignTitle",
          Value:
            searchCampaignQuery?.length > 0 ? searchCampaignQuery : undefined,
        },
      },
    };
    debounceFetchCampaignCount(data);
  }, [selectedFilter1, searchCampaignQuery]);

  const debouncedFetchCampaignData = useCallback(
    debounce(async (data) => {
      dispatch(getCampaign(data));
    }, 500),
    []
  );

  const fetchCampaignData = useCallback(
    (pageNumber = page) => {
      const campaignData = {
        page: pageNumber,
        perPage: rowsPerPage,
        filters: {
          searching: {
            column: "campaignTitle",
            value:
              searchCampaignQuery?.length > 0 ? searchCampaignQuery : undefined,
          },
          filtering: {
            filterType: "and",
            conditions: [
              {
                column: "dateSetLive",
                operator: selectedFilter1.value.toLowerCase(),
                value: new Date().toISOString().slice(0, 10),
              },
            ],
          },
        },
      };
      debouncedFetchCampaignData(campaignData);
    },
    [
      selectedFilter1,
      searchCampaignQuery,
      rowsPerPage,
      debouncedFetchCampaignData,
    ]
  );

  useEffect(() => {
    fetchCampaignData();
  }, [selectedFilter1, searchCampaignQuery, page, fetchCampaignData]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    // Convert UTC to IST by adding 5 hours and 30 minutes
    date.setHours(date.getHours() + 5);
    date.setMinutes(date.getMinutes() + 30);

    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    const hours = ("0" + date.getHours()).slice(-2);
    const minutes = ("0" + date.getMinutes()).slice(-2);
    const seconds = ("0" + date.getSeconds()).slice(-2);
    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  };

  const handlePageChange = (event: any, value: number) => {
    setPage(value);
  };

  const columns: TableColumn[] = [
    {
      id: "campaignTitle",
      label: "Title",
      width: "100px",
      format: (value) => (
        <Tooltip title={value} placement="top">
          <Box
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "250px",
              paddingLeft: "20px",
            }}
          >
            {value}
          </Box>
        </Tooltip>
      ),
    },
    {
      id: "createdby",
      label: "Created by",
      width: "100px",
    },
    {
      id: "state",
      label: "State",
      width: "100px",
      format: (value) => {
        const stateMap = {
          1: { label: "Completed", color: "success" },
          2: { label: "Incomplete", color: "error" },
          3: { label: "Scheduled", color: "warning" },
        };
        const state = stateMap[value as keyof typeof stateMap];
        return (
          <Chip
            label={state?.label || ""}
            color={state?.color as any}
            size="small"
          />
        );
      },
    },
    {
      id: "createdDate",
      label: "Created Date",
      width: "120px",
      format: (value) => formatDate(value),
    },
    {
      id: "dateSetLive",
      label: "Date Set Live",
      width: "120px",
      format: (value) => formatDate(value),
    },
  ];
  const renderActions = (row: any) => (
    <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
      <Tooltip title="View Stats" placement="bottom">
        <Box>
          <IconButton onClick={(event) => handleViewReport(row, event)}>
            <VisibilityOutlinedIcon />
          </IconButton>
        </Box>
      </Tooltip>
      <Tooltip title="Download" placement="bottom">
        <Box>
          <IconButton
            onClick={() => handleDownloadReport(row)}
            disabled={downloadingReport === row.campaignId}
          >
            {downloadingReport === row.campaignId ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              <FileDownloadOutlinedIcon />
            )}
          </IconButton>
        </Box>
      </Tooltip>
      <Tooltip title="Resend" placement="bottom">
        <Box>
          <IconButton onClick={() => handleResendCampaign(row)}>
            <SyncIcon />
          </IconButton>
        </Box>
      </Tooltip>
    </Box>
  );
  const renderCampaignMobile = (row: any) => {
    return (
      <Card sx={{ m: 1, p: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            flexWrap: "wrap",
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {row.campaignTitle}
          </Typography>
          <Chip
            label={
              row.state === 1
                ? "Completed"
                : row.state === 2
                ? "Incomplete"
                : "Scheduled"
            }
            color={
              row.state === 1
                ? "success"
                : row.state === 2
                ? "error"
                : "warning"
            }
            size="small"
          />
        </Box>
        <CardContent sx={{ p: 0, "&:last-child": { paddingBottom: 0 } }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Created by
              </Typography>
              <Typography variant="body2">{row.createdby}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Date Set Live
              </Typography>
              <Typography variant="body2">
                {formatDate(row.dateSetLive)}
              </Typography>
            </Grid>
          </Grid>
          <Box sx={{ mt: 1, display: "flex", justifyContent: "flex-end" }}>
            <Tooltip title="View Stats" placement="bottom">
              <Box>
                <IconButton onClick={(event) => handleViewReport(row, event)}>
                  <VisibilityOutlinedIcon />
                </IconButton>
              </Box>
            </Tooltip>
            <Tooltip title="Download" placement="bottom">
              <Box>
                <IconButton
                  onClick={() => handleDownloadReport(row)}
                  disabled={downloadingReport === row.campaignId}
                >
                  {downloadingReport === row.campaignId ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    <FileDownloadOutlinedIcon />
                  )}
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleResendCampaign(row)}
                >
                  <SyncIcon />
                </IconButton>
              </Box>
            </Tooltip>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      {hasOnetimeCampaignsPermission ? (
        <Grid className={classes.mainContainer}>
          <Box className={classes.bgContainer}>
            <CommonTable
              columns={columns}
              data={pageData}
              rowIdKey="campaignId"
              title="Campaigns"
              count={campaignsCount}
              page={page}
              onPageChange={handlePageChange}
              totalPages={Math.ceil(campaignsCount / rowsPerPage)}
              actions={renderActions}
              showPagination={true}
              isLoading={getCampaignAnalyticsStatus === "loading"}
              renderOnMobile={renderCampaignMobile}
              searchProps={{
                value: searchCampaignQuery,
                onChange: setSearchCampaignQuery,
                placeholder: "Search campaigns...",
              }}
              primaryAction={{
                label: "Add Campaign",
                onClick: handleOpenDialog,
                icon: <Add />,
                show: hasAccess("newCampaign"),
              }}
              selectedMainFilter={selectedFilter1}
              handleMainFilter={handleFilterClick1}
              perPage={rowsPerPage}
            />
          </Box>
          <EditCampaign
            title="Add"
            data={null}
            open={openDialog}
            handleClose={handleCloseDialog}
            isCampaignSaved={isCampaignSaved}
            setIsCampaignSaved={setIsCampaignSaved}
            createCampaignState={createCampaignState}
            setCreateCampaignState={setCreateCampaignState}
            searchCampaignQuery={searchCampaignQuery}
            selectedFilter1={selectedFilter1}
          />
          <ResendCampaignPopup
            open={openResendDialog}
            title={title}
            handleClose={handleCloseResendDialog}
            data={resendCampaignData}
            setResendCampaignData={setResendCampaignData}
            searchCampaignQuery={searchCampaignQuery}
            selectedFilter1={selectedFilter1}
            selectedCampaignAnalytics={selectedCampaignAnalytics}
            onCampaignRerun={fetchCampaignData}
          />
          <CampaignFilterPopOvers
            anchorEl={anchorEl1}
            handleClose={handleClose1}
            options={options1}
            handleOptionClick={handleOptionClick}
          />
          <Popover
            open={Boolean(analyticsPopoverAnchorEl)}
            anchorEl={analyticsPopoverAnchorEl}
            onClose={handleCloseAnalyticsPopover}
            anchorOrigin={{
              vertical: "center", // Changed from 'bottom' to 'center'
              horizontal: "right", // Changed from 'left' to 'right'
            }}
            transformOrigin={{
              vertical: "center", // Changed from 'top' to 'center'
              horizontal: "left", // Keep as 'left'
            }}
            PaperProps={{
              sx: {
                boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.15)",
                borderRadius: "8px",
                maxHeight: "80vh", // Prevent popover from being too tall
                overflowY: "auto", // Add scroll if content is too long
              },
            }}
          >
            {isCampaignDataLoading ? (
              <Box sx={{ p: 2 }}>
                <LoadingComponent height="100px" color={bgColors.blue} />
              </Box>
            ) : (
              selectedCampaignAnalytics && (
                <Box
                  sx={{
                    p: 2,
                    pt: 3,
                    minWidth: "auto",
                    maxWidth: 400,
                    "& > *:not(:last-child)": { mb: 1.5 },
                  }}
                >
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Campaign Analytics
                  </Typography>

                  {/* Add your analytics data display here */}
                  <Box
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          Attempted
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.attemptedCount || 0}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          Failed
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.failedCount || 0}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          Replied
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.repliedCount || 0}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          Delivered
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.deliveredCount || 0}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          Read By
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.readCount || 0}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="textSecondary">
                          UnDelivered
                        </Typography>
                        <Typography>
                          {selectedCampaignAnalytics.undelivered || 0}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      position: "absolute",
                      right: 0,
                      top: 0,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <IconButton onClick={handleCloseAnalyticsPopover}>
                      <CloseSvg />
                    </IconButton>
                  </Box>

                  {/* Add more analytics data fields as needed */}
                </Box>
              )
            )}
          </Popover>
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Campaigns;
