/* global process */

import axios from "axios";
import { getStoredTokens } from "../../../utils/authUtils";

const WORKFLOW_API_URL = process.env.REACT_APP_BASE_URL;

const WORKFLOW_REACTFLOW_API_URL = process.env.REACT_APP_WEB_SOCKET_BASE_URL;

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};

const getWorkflowList = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflowList?companyId=${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflow?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getWorkflowNames = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflowNames/${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getVariableName = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetVeriableNames?companyId=${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const deleteWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/DeleteWorkflow?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const createWorkflowCustomMessage = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AddWorkflowList`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const createWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/CreateWorkflow/${data?.isNew}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data.workflowData,
  });
};

const updateWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/UpdateWorkflow`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data.workflowData,
  });
};

const updateWorkflowList = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/updateWorkflowList`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const downloadWorkflowResponseListByName = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/DownloadWorkflowResponseListByName?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    responseType: "blob",
  });
};

// select a response

const getSelectResponseData = (payload: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${payload}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const createSelectResponse = (companyId: string, response: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${companyId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: response,
  });
};

const updateSelectResponse = (selectResponseId: string, response: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${selectResponseId}`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: response,
  });
};

const deleteSelectResponse = (selectResponseId: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${selectResponseId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

// Workflow Rectflow apis
const createWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};
const updateWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/${data?.id}`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.data,
  });
};

const getAllWorkflowsReactflow = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};
const deleteWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/${data?.companyId}/${data?.WorkflowId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getWorkflowReactflowById = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/GetWorkflowById/${data}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const createKeywordReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/AddKeyword`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const deleteKeywordReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/DeleteKeywords/${data?.workflowId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.keywords,
  });
};

const getSourcesAndSubSources = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/GetAllSubSourcesBySource?BusinessId=${data.businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getWorkflowAllKeywords = (id: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/GetAllKeywords/${id}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const AddSaveResponseAttribute = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AttributeName`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const getSaveResponseAttribute = () => {
  return axios({
    url: `${WORKFLOW_API_URL}/AttributeName`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const saveWorkflowCustomResponse = (payload: {
  businessId: string;
  nodeId: string;
  userId: string;
  attributeId: string;
}) => {
  return axios({
    url: `${WORKFLOW_API_URL}/WorkflowCustomResponse`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: payload,
  });
};

const getKeywords = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/GetKeywords/${data?.companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};
const toggleWorkflowActive = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/ToggleActive/${data?.workflowId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};
const getFlowstartNodes = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/workflow/GetFlowstartNodes/${data?.businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};
const deleteAttribute = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/AttributeName/${data?.id}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

export const WORKFLOW_API = {
  getWorkflowList,
  getVariableName,
  deleteWorkflow,
  createWorkflowCustomMessage,
  updateWorkflowList,
  createWorkflow,
  getWorkflowNames,
  getSelectResponseData,
  getWorkflow,
  updateWorkflow,
  downloadWorkflowResponseListByName,
  createSelectResponse,
  updateSelectResponse,
  deleteSelectResponse,
  // workflow Reactflow
  createWorkflowReactflow,
  updateWorkflowReactflow,
  getAllWorkflowsReactflow,
  deleteWorkflowReactflow,
  getWorkflowReactflowById,
  createKeywordReactflow,
  deleteKeywordReactflow,
  getSourcesAndSubSources,
  getWorkflowAllKeywords,
  AddSaveResponseAttribute,
  getSaveResponseAttribute,
  saveWorkflowCustomResponse,
  getKeywords,
  toggleWorkflowActive,
  getFlowstartNodes,
  deleteAttribute,
};
